<Window x:Class="MemoryEnhancer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MemoryEnhancer"
        mc:Ignorable="d"
        Title="记忆增强器" Height="Auto" Width="Auto" MinWidth="300" SizeToContent="WidthAndHeight"
        Background="Transparent" WindowStyle="None" AllowsTransparency="True"
        Topmost="True" ResizeMode="NoResize" MouseLeftButtonDown="Window_MouseLeftButtonDown"
        WindowStartupLocation="Manual" Left="10">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="25"/>
        </Grid.RowDefinitions>

        <!-- 内容显示区域 -->
        <Border x:Name="contentBorder" Grid.Row="0" Background="White" CornerRadius="5" Margin="2" MinHeight="30">
            <TextBlock x:Name="contentText" TextWrapping="Wrap" Padding="10,5"
                       VerticalAlignment="Center" HorizontalAlignment="Left"/>
        </Border>

        <!-- 控制按钮区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left" Margin="5,0,0,0">
            <Button x:Name="prevButton" Height="20" Width="20" Margin="2" Click="PrevButton_Click" ToolTip="上一条">
                <Path Data="{StaticResource PrevIcon}" Fill="Black" Stretch="Uniform" />
            </Button>
            <Button x:Name="nextButton" Height="20" Width="20" Margin="2" Click="NextButton_Click" ToolTip="下一条">
                <Path Data="{StaticResource NextIcon}" Fill="Black" Stretch="Uniform" />
            </Button>
            <Button x:Name="playPauseButton" Height="20" Width="20" Margin="2" Click="PlayPauseButton_Click" ToolTip="播放/暂停">
                <Path x:Name="playPauseIcon" Data="{StaticResource PlayIcon}" Fill="Black" Stretch="Uniform" />
            </Button>
            <Button x:Name="playButton" Height="20" Width="20" Margin="2" Click="PlayButton_Click" ToolTip="朗读">
                <Path x:Name="speakIcon" Data="{StaticResource SpeakIcon}" Fill="Black" Stretch="Uniform" />
            </Button>
            <Button x:Name="easyButton" Height="20" Width="20" Margin="2" Click="EasyButton_Click" ToolTip="简单">
                <Path Data="{StaticResource EasyIcon}" Fill="Green" Stretch="Uniform" />
            </Button>
            <Button x:Name="hardButton" Height="20" Width="20" Margin="2" Click="HardButton_Click" ToolTip="困难">
                <Path Data="{StaticResource HardIcon}" Fill="Red" Stretch="Uniform" />
            </Button>
            <Button x:Name="settingsButton" Height="20" Width="20" Margin="2" Click="SettingsButton_Click" ToolTip="设置">
                <Path Data="{StaticResource SettingsIcon}" Fill="Black" Stretch="Uniform" />
            </Button>
            <Button x:Name="closeButton" Height="20" Width="20" Margin="2" Click="CloseButton_Click" ToolTip="关闭">
                <Path Data="{StaticResource CloseIcon}" Fill="Black" Stretch="Uniform" />
            </Button>
        </StackPanel>
    </Grid>
</Window>