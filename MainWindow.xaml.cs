using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using MemoryEnhancer.Exceptions;
using MemoryEnhancer.Utils;


namespace MemoryEnhancer
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private List<MemoryItem> memoryItems = new List<MemoryItem>();
        private int currentIndex = 0;
        private SimpleTTSHelper? ttsHelper = null;
        private DispatcherTimer intervalTimer = new DispatcherTimer(); // 用于两条内容之间的间隔时间
        private readonly DispatcherTimer saveTimer = new DispatcherTimer(); // 用于延迟保存数据
        private UserSettings settings = new UserSettings();
        private bool isSpeaking = false;
        private bool isReadingStopped = false; // 用户是否主动停止了朗读功能
        private bool hasUnsavedChanges = false; // 是否有未保存的更改
        private bool isTtsAvailable = false; // TTS功能是否可用
        private int currentRepeatCount = 0; // 当前项目已重复的次数

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                // 清理旧日志文件
                Logger.CleanupOldLogs();
                Logger.Info("应用程序启动");

                // 初始化语音合成器
                InitializeTTS();

                // 加载用户设置
                LoadSettings();

                // 文件组选择现在在设置对话框中管理

                // 初始化显示计时器
                InitializeTimer();

                // 初始化保存计时器
                InitializeSaveTimer();

                // 加载记忆内容
                LoadMemoryItems();

                // 显示第一条内容
                DisplayCurrentItem();

                // 启用键盘事件处理
                KeyDown += MainWindow_KeyDown;
                Focusable = true;

                // 设置窗口位置到左下角
                SetWindowPositionToBottomLeft();

                Logger.Info("应用程序初始化完成");
            }
            catch (Exception ex)
            {
                Logger.Error("应用程序初始化失败", ex);
                MessageBox.Show($"应用程序初始化失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // 尝试优雅关闭
                Application.Current.Shutdown();
            }
        }

        // 文件组选择功能已移到设置对话框中

        /// <summary>
        /// 设置窗口位置到左下角
        /// </summary>
        private void SetWindowPositionToBottomLeft()
        {
            try
            {
                // 获取屏幕工作区域（排除任务栏）
                var workingArea = SystemParameters.WorkArea;

                // 设置窗口位置：左边距10像素，底部距离10像素
                Left = 10;
                Top = workingArea.Height - ActualHeight - 10;

                // 如果窗口还没有渲染完成，使用Loaded事件来设置位置
                if (ActualHeight == 0)
                {
                    Loaded += (sender, e) =>
                    {
                        Top = workingArea.Height - ActualHeight - 10;
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.Warning($"设置窗口位置时出错: {ex.Message}");
                // 如果设置失败，使用默认位置
                Left = 10;
                Top = 100;
            }
        }

        /// <summary>
        /// 初始化TTS语音合成器，使用简化的Windows内置SAPI
        /// </summary>
        private void InitializeTTS()
        {
            try
            {
                Logger.Info("开始初始化简化TTS功能");

                // 检查操作系统版本
                var osVersion = Environment.OSVersion;
                bool isWindows7 = osVersion.Version.Major == 6 && osVersion.Version.Minor == 1;
                Logger.Info($"操作系统版本: {osVersion.VersionString}, Windows 7: {isWindows7}");

                // 运行简化TTS诊断测试
                var diagnosticResult = SimpleTTSDiagnostic.RunDiagnostic();
                Logger.Info($"TTS诊断结果: {diagnosticResult.GetStatusDescription()}");

                // 根据诊断结果决定TTS初始化策略
                if (diagnosticResult.OverallStatus == SimpleTTSStatus.NotAvailable ||
                    diagnosticResult.OverallStatus == SimpleTTSStatus.Error)
                {
                    isTtsAvailable = false;
                    Logger.Warning("TTS诊断检查失败，程序将以静音模式运行");

                    // 显示诊断信息
                    if (isWindows7)
                    {
                        ShowWindows7SimpleTTSDiagnosticMessage(diagnosticResult);
                    }
                    else
                    {
                        ShowSimpleTTSDiagnosticFailedMessage(diagnosticResult);
                    }
                    return;
                }

                // 尝试创建简化TTS助手
                if (!TryCreateTTSHelper())
                {
                    isTtsAvailable = false;
                    Logger.Warning("无法创建TTS助手，程序将以静音模式运行");
                    return;
                }

                // 测试TTS功能是否可用
                int voiceCount = ttsHelper?.GetVoiceCount() ?? 0;
                Logger.Info($"检测到 {voiceCount} 个已安装的语音");

                if (voiceCount > 0)
                {
                    // 测试语音合成器是否真正可用
                    if (TestTTSFunctionality())
                    {
                        isTtsAvailable = true;
                        Logger.Info("TTS功能测试成功");

                        // 在Windows 7上显示成功消息
                        if (isWindows7)
                        {
                            Logger.Info("Windows 7 TTS功能初始化成功");
                        }
                    }
                    else
                    {
                        isTtsAvailable = false;
                        Logger.Warning("TTS功能测试失败");
                        if (isWindows7)
                        {
                            ShowWindows7TTSTestFailedMessage();
                        }
                        else
                        {
                            ShowTTSTestFailedMessage();
                        }
                    }
                }
                else
                {
                    isTtsAvailable = false;
                    Logger.Warning("系统中没有可用的TTS语音");
                    if (isWindows7)
                    {
                        ShowWindows7NoVoicesMessage();
                    }
                    else
                    {
                        ShowNoVoicesMessage();
                    }
                }
            }
            catch (Exception ex)
            {
                isTtsAvailable = false;
                Logger.Error("TTS初始化失败", ex);

                // 确保程序能继续运行
                try
                {
                    ttsHelper?.Dispose();
                    ttsHelper = null;
                }
                catch (Exception disposeEx)
                {
                    Logger.Warning($"释放TTS资源时出错: {disposeEx.Message}");
                }

                ShowTTSInitializationError(ex);
            }
        }

        /// <summary>
        /// 尝试创建TTS助手
        /// </summary>
        private bool TryCreateTTSHelper()
        {
            try
            {
                ttsHelper = new SimpleTTSHelper();
                ttsHelper.SpeakCompleted += TtsHelper_SpeakCompleted;
                bool initialized = ttsHelper.Initialize();
                if (initialized)
                {
                    Logger.Info("TTS助手创建成功");
                    return true;
                }
                else
                {
                    Logger.Warning("TTS助手初始化失败");
                    ttsHelper.Dispose();
                    ttsHelper = null;
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("创建TTS助手失败", ex);
                ttsHelper?.Dispose();
                ttsHelper = null;
                return false;
            }
        }

        /// <summary>
        /// TTS助手朗读完成事件处理器
        /// </summary>
        private void TtsHelper_SpeakCompleted(object? sender, EventArgs e)
        {
            isSpeaking = false;

            // 更新朗读按钮状态
            UpdateSpeakButtonState(false);

            // 检查是否需要重复播放当前内容
            currentRepeatCount++;
            if (currentRepeatCount < settings.RepeatCount)
            {
                // 还需要重复播放，继续朗读当前内容
                Logger.Info($"重复播放第 {currentRepeatCount + 1} 次，总共需要 {settings.RepeatCount} 次");
                ReadCurrentItem();
                return;
            }

            // 重复播放完成，重置计数器
            currentRepeatCount = 0;

            // 朗读完成后，如果自动前进开启，启动间隔计时器
            if (settings.AutoAdvance)
            {
                // 启动间隔计时器，等待设置的间隔时间后再跳转
                intervalTimer.Start();
            }
        }

        /// <summary>
        /// 测试TTS功能是否真正可用
        /// </summary>
        private bool TestTTSFunctionality()
        {
            try
            {
                if (ttsHelper == null || !ttsHelper.IsAvailable)
                    return false;

                // 使用TTS助手进行测试
                return ttsHelper.TestTTS();
            }
            catch (Exception ex)
            {
                Logger.Error("TTS功能测试失败", ex);
                return false;
            }
        }

        // 简化版TTS不需要配置语音，使用系统默认设置

        /// <summary>
        /// 显示TTS不可用消息
        /// </summary>
        private void ShowTTSUnavailableMessage()
        {
            var message = "语音合成功能不可用。\n\n" +
                         "程序将以静音模式运行，您仍可以正常使用其他功能。\n\n" +
                         "如需启用语音功能，请确保：\n" +
                         "1. 系统已安装语音包\n" +
                         "2. Windows语音服务正在运行\n" +
                         "3. 音频设备工作正常";

            MessageBox.Show(message, "语音功能提示",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示Windows 7专用的TTS测试失败消息
        /// </summary>
        private void ShowWindows7TTSTestFailedMessage()
        {
            var message = "Windows 7 语音合成功能测试失败。\n\n" +
                         "检测到语音引擎，但无法正常工作。\n\n" +
                         "Windows 7 专用解决方案：\n" +
                         "1. 检查 Windows Audio 服务是否运行\n" +
                         "2. 重新安装音频驱动程序\n" +
                         "3. 安装 Microsoft Speech Platform Runtime 11.0\n" +
                         "4. 以管理员身份运行程序\n" +
                         "5. 重启计算机\n\n" +
                         "如果问题持续，请运行 Windows7-Diagnostic.bat 获取详细诊断。";

            MessageBox.Show(message, "Windows 7 语音功能提示",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// 显示TTS测试失败消息
        /// </summary>
        private void ShowTTSTestFailedMessage()
        {
            var message = "语音合成功能测试失败。\n\n" +
                         "检测到语音引擎，但无法正常工作。\n\n" +
                         "建议解决方案：\n" +
                         "1. 重启计算机\n" +
                         "2. 检查音频设备\n" +
                         "3. 更新音频驱动程序";

            MessageBox.Show(message, "语音功能提示",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// 显示Windows 7专用的无语音包消息
        /// </summary>
        private void ShowWindows7NoVoicesMessage()
        {
            var message = "Windows 7 系统中没有安装语音包。\n\n" +
                         "程序将以静音模式运行。\n\n" +
                         "Windows 7 语音包安装方法：\n" +
                         "1. 控制面板 → 语音识别 → 文本到语音转换\n" +
                         "2. 下载 Microsoft Speech Platform Runtime 11.0\n" +
                         "3. 下载中文语音包（如 MSSpeech_TTS_zh-CN_HuiHui.msi）\n" +
                         "4. 安装完成后重启程序\n\n" +
                         "或运行 Windows7-Diagnostic.bat 获取详细安装指导。";

            MessageBox.Show(message, "Windows 7 语音功能提示",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示无语音包消息
        /// </summary>
        private void ShowNoVoicesMessage()
        {
            var message = "系统中没有安装语音包。\n\n" +
                         "程序将以静音模式运行。\n\n" +
                         "如需启用语音功能，请：\n" +
                         "1. 在控制面板中安装语音包\n" +
                         "2. 或从Microsoft官网下载语音包\n" +
                         "3. 安装完成后重启程序";

            MessageBox.Show(message, "语音功能提示",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示Windows 7专用的TTS诊断失败消息
        /// </summary>
        private void ShowWindows7SimpleTTSDiagnosticMessage(SimpleTTSDiagnosticResult diagnosticResult)
        {
            var message = "Windows 7 语音合成功能需要额外安装组件\n\n";

            message += "🔍 诊断结果：\n";
            message += $"• 创建语音合成器：{(diagnosticResult.CanCreateSynthesizer ? "✅ 成功" : "❌ 失败")}\n";
            message += $"• 获取语音列表：{(diagnosticResult.CanGetVoices ? "✅ 成功" : "❌ 失败")}\n";
            message += $"• 语音合成测试：{(diagnosticResult.CanSynthesize ? "✅ 成功" : "❌ 失败")}\n";
            message += $"• 可用语音数量：{diagnosticResult.VoiceCount}\n";
            message += $"• 系统服务状态：{(diagnosticResult.ServicesRunning ? "✅ 正常" : "❌ 异常")}\n\n";

            if (!string.IsNullOrEmpty(diagnosticResult.ErrorMessage))
            {
                message += $"❗ 错误详情：{diagnosticResult.ErrorMessage}\n\n";
            }

            message += "📋 Windows 7 解决方案（按顺序执行）：\n\n";
            message += "1️⃣ 下载并安装 Microsoft Speech Platform Runtime 11.0\n";
            message += "   • 访问 Microsoft 官网下载 SpeechPlatformRuntime.msi\n";
            message += "   • 选择 x64 版本（约 5MB）\n\n";

            message += "2️⃣ 下载并安装中文语音包\n";
            message += "   • 下载 MSSpeech_TTS_zh-CN_HuiHui.msi（女声）\n";
            message += "   • 或 MSSpeech_TTS_zh-CN_Kangkang.msi（男声）\n";
            message += "   • 文件大小约 50-100MB\n\n";

            message += "3️⃣ 重启计算机\n";
            message += "   • 安装完成后必须重启系统\n\n";

            message += "4️⃣ 验证安装\n";
            message += "   • 控制面板 → 语音识别 → 文本到语音转换\n";
            message += "   • 选择语音并点击\"预览\"测试\n\n";

            message += "🛠️ 快速工具：\n";
            message += "• 运行 Windows7-TTS-Setup.bat 获取安装指导\n";
            message += "• 运行 Windows7-Diagnostic.bat 进行详细诊断\n";
            message += "• 查看 Windows7-用户手册.md 获取完整说明\n\n";

            message += "💡 程序将以静音模式继续运行，其他功能正常使用。";

            MessageBox.Show(message, "Windows 7 语音功能安装指导",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 显示TTS诊断失败消息
        /// </summary>
        private void ShowSimpleTTSDiagnosticFailedMessage(SimpleTTSDiagnosticResult diagnosticResult)
        {
            var message = "语音合成功能诊断失败。\n\n";
            message += $"诊断状态：{diagnosticResult.GetStatusDescription()}\n\n";

            message += "详细信息：\n";
            message += $"• 创建语音合成器：{(diagnosticResult.CanCreateSynthesizer ? "成功" : "失败")}\n";
            message += $"• 获取语音列表：{(diagnosticResult.CanGetVoices ? "成功" : "失败")}\n";
            message += $"• 语音合成测试：{(diagnosticResult.CanSynthesize ? "成功" : "失败")}\n";
            message += $"• 可用语音数量：{diagnosticResult.VoiceCount}\n";
            message += $"• 系统服务状态：{(diagnosticResult.ServicesRunning ? "正常" : "异常")}\n\n";

            if (!string.IsNullOrEmpty(diagnosticResult.ErrorMessage))
            {
                message += $"错误信息：{diagnosticResult.ErrorMessage}\n\n";
            }

            message += "建议解决方案：\n";

            if (!diagnosticResult.CanCreateSynthesizer)
            {
                message += "• 重新安装Microsoft Speech Platform\n";
                message += "• 检查.NET Framework版本\n";
            }

            if (diagnosticResult.VoiceCount == 0)
            {
                message += "• 安装Windows语音包\n";
                message += "• 从控制面板启用语音功能\n";
            }

            if (!diagnosticResult.ServicesRunning)
            {
                message += "• 启动Windows Audio服务\n";
                message += "• 检查音频设备驱动程序\n";
            }

            message += "• 以管理员身份运行程序\n";
            message += "• 重启计算机\n\n";
            message += "程序将以静音模式继续运行。";

            MessageBox.Show(message, "语音功能诊断",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// 显示TTS初始化错误消息
        /// </summary>
        private void ShowTTSInitializationError(Exception ex)
        {
            string errorMessage = "语音合成功能初始化失败。\n\n";

            if (ex.Message.Contains("SAPI") || ex.Message.Contains("Speech"))
            {
                errorMessage += "这是Windows语音API相关错误。\n\n" +
                              "可能的解决方案：\n" +
                              "1. 确保Windows语音识别服务正在运行\n" +
                              "2. 检查系统是否安装了语音包\n" +
                              "3. 尝试重启程序或重启计算机\n" +
                              "4. 检查Windows更新";
            }
            else if (ex.Message.Contains("Access") || ex.Message.Contains("权限"))
            {
                errorMessage += "这可能是权限问题。\n\n" +
                              "建议解决方案：\n" +
                              "1. 以管理员身份运行程序\n" +
                              "2. 检查防病毒软件设置\n" +
                              "3. 确保程序有访问音频设备的权限";
            }
            else
            {
                errorMessage += $"错误详情：{ex.Message}\n\n" +
                              "通用解决方案：\n" +
                              "1. 重启计算机\n" +
                              "2. 检查Windows更新\n" +
                              "3. 重新安装程序";
            }

            errorMessage += "\n\n程序将继续运行，但语音功能将不可用。";

            MessageBox.Show(errorMessage, "语音功能初始化失败",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }

        /// <summary>
        /// 显示TTS诊断信息
        /// </summary>
        private void ShowTTSDiagnosticInfo()
        {
            try
            {
                // 重新运行简化诊断以获取最新状态
                var diagnosticResult = SimpleTTSDiagnostic.RunDiagnostic();

                // 检查操作系统版本
                var osVersion = Environment.OSVersion;
                bool isWindows7 = osVersion.Version.Major == 6 && osVersion.Version.Minor == 1;

                if (isWindows7)
                {
                    ShowWindows7SimpleTTSDiagnosticMessage(diagnosticResult);
                }
                else
                {
                    ShowSimpleTTSDiagnosticFailedMessage(diagnosticResult);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("运行TTS诊断时出错", ex);
                MessageBox.Show($"无法运行TTS诊断：{ex.Message}\n\n请检查系统状态或联系技术支持。",
                    "诊断错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeSaveTimer()
        {
            // 保存计时器：延迟指定时间后保存数据，避免频繁磁盘操作
            saveTimer.Interval = TimeSpan.FromSeconds(Constants.DelaySaveTimeSeconds);
            saveTimer.Tick += SaveTimer_Tick;
        }

        private void SaveTimer_Tick(object? sender, EventArgs e)
        {
            saveTimer.Stop();
            if (hasUnsavedChanges)
            {
                SaveAllData();
                hasUnsavedChanges = false;
            }
        }

        /// <summary>
        /// 安排延迟保存数据
        /// </summary>
        private void ScheduleDataSave()
        {
            hasUnsavedChanges = true;
            saveTimer.Stop();
            saveTimer.Start();
        }

        /// <summary>
        /// 保存所有数据
        /// </summary>
        private void SaveAllData()
        {
            try
            {
                MemoryManager.SaveMemoryItems(memoryItems);
                SettingsManager.SaveSettings(settings);
                MemoryManager.SaveStatistics(settings);
                Logger.Info("所有数据保存成功");
            }
            catch (DataAccessException ex)
            {
                ShowErrorMessage($"保存数据失败: {ex.Message}", ex);
            }
            catch (SettingsException ex)
            {
                ShowErrorMessage($"保存设置失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"保存数据时发生未知错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="logError">是否记录到日志</param>
        private void ShowErrorMessage(string message, bool logError = true)
        {
            if (logError)
            {
                Logger.Error(message);
            }

            MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// 显示错误消息（包含异常信息）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常对象</param>
        private void ShowErrorMessage(string message, Exception exception)
        {
            Logger.Error(message, exception);
            MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void InitializeTimer()
        {
            // 间隔计时器：用于两条内容之间的间隔时间
            intervalTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(settings.IntervalTimeInSeconds)
            };
            intervalTimer.Tick += IntervalTimer_Tick;

            // 根据设置初始化播放状态
            if (settings.AutoAdvance)
            {
                UpdatePlayPauseButtonState(true);
                Logger.Info($"自动播放已启用，AutoRead: {settings.AutoRead}");

                // 如果没有开启自动朗读，立即启动计时器
                if (!settings.AutoRead)
                {
                    Logger.Info("自动朗读未开启，立即启动间隔计时器");
                    intervalTimer.Start();
                }
                else
                {
                    Logger.Info("自动朗读已开启，等待第一次显示内容时开始朗读");
                }
            }
            else
            {
                UpdatePlayPauseButtonState(false);
                Logger.Info("自动播放未启用");
            }
        }

        private void IntervalTimer_Tick(object? sender, EventArgs e)
        {
            // 间隔时间到了，自动跳转到下一条
            intervalTimer.Stop();
            AutoAdvanceToNext();
        }

        private void AutoAdvanceToNext()
        {
            if (memoryItems.Count == 0)
                return;

            // 重置重复计数器
            currentRepeatCount = 0;

            currentIndex = (currentIndex + 1) % memoryItems.Count;
            DisplayCurrentItem(); // 使用正常的显示逻辑，包括自动朗读
            SaveProgress();
        }

        private void UpdatePlayPauseButtonState(bool isPlaying)
        {
            try
            {
                // 通过名称查找控件，避免编译时引用问题
                var playPauseBtn = this.FindName("playPauseButton") as System.Windows.Controls.Button;
                var playPauseIconPath = this.FindName("playPauseIcon") as System.Windows.Shapes.Path;

                if (playPauseBtn != null && playPauseIconPath != null)
                {
                    if (isPlaying)
                    {
                        playPauseBtn.ToolTip = "暂停";
                        // 设置暂停图标
                        playPauseIconPath.Data = (PathGeometry)Application.Current.Resources["PauseIcon"];
                    }
                    else
                    {
                        playPauseBtn.ToolTip = "播放";
                        // 设置播放图标
                        playPauseIconPath.Data = (PathGeometry)Application.Current.Resources["PlayIcon"];
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响程序运行
                Logger.Warning($"更新播放暂停按钮状态时出错: {ex.Message}");
            }
        }



        // 旧的Synthesizer_SpeakCompleted方法已被TtsHelper_SpeakCompleted替代

        private void LoadSettings()
        {
            // 从本地存储加载设置
            settings = SettingsManager.LoadSettings();

            // 加载统计数据
            MemoryManager.LoadStatistics(settings);

            // 应用设置到UI
            ApplySettings();
        }

        private void ApplySettings()
        {
            try
            {
                // 通过名称查找控件，避免编译时引用问题
                var contentTextBlock = this.FindName("contentText") as System.Windows.Controls.TextBlock;
                var contentBorderElement = this.FindName("contentBorder") as System.Windows.Controls.Border;

                if (contentTextBlock != null)
                {
                    // 应用颜色设置
                    contentTextBlock.Foreground = new SolidColorBrush(settings.GetTextColor());

                    // 应用字体设置
                    contentTextBlock.FontFamily = new FontFamily(settings.FontFamily);
                    contentTextBlock.FontSize = settings.FontSize;
                    contentTextBlock.FontWeight = settings.GetFontWeight();
                    contentTextBlock.FontStyle = settings.GetFontStyle();
                }

                if (contentBorderElement != null)
                {
                    contentBorderElement.Background = new SolidColorBrush(settings.GetBackgroundColor());
                }
            }
            catch (Exception ex)
            {
                Logger.Warning($"应用设置时出错: {ex.Message}");
            }

            // 应用计时器设置
            if (intervalTimer != null)
            {
                // 记录当前播放状态
                bool wasPlaying = IsAutoPlaying();

                // 停止当前计时器
                intervalTimer.Stop();

                // 更新间隔时间
                intervalTimer.Interval = TimeSpan.FromSeconds(settings.IntervalTimeInSeconds);

                // 只有在之前正在播放时才恢复播放状态
                if (wasPlaying && settings.AutoAdvance)
                {
                    // 恢复播放状态，但不立即启动，让用户决定何时开始
                    UpdatePlayPauseButtonState(true);
                }
                else
                {
                    // 确保按钮状态与设置一致
                    UpdatePlayPauseButtonState(settings.AutoAdvance);
                }
            }
        }

        private void LoadMemoryItems()
        {
            try
            {
                // 使用新的文件分组系统加载记忆项目
                memoryItems = MemoryManager.GetFileGroupItems(settings);

                // 如果没有项目，尝试从旧的统一列表加载
                if (memoryItems.Count == 0)
                {
                    memoryItems = MemoryManager.GetAllAvailableItems();
                }

                // 加载进度
                if (!string.IsNullOrEmpty(settings.CurrentFileGroupId))
                {
                    currentIndex = MemoryManager.LoadFileGroupProgress(settings.CurrentFileGroupId);
                }
                else
                {
                    currentIndex = MemoryManager.LoadProgress();
                }

                // 确保索引在有效范围内
                if (memoryItems.Count > 0 && (currentIndex < 0 || currentIndex >= memoryItems.Count))
                {
                    currentIndex = 0;
                }

                Logger.Info($"加载了 {memoryItems.Count} 个记忆项目，当前索引: {currentIndex}");
            }
            catch (DataAccessException ex)
            {
                ShowErrorMessage($"加载记忆内容失败: {ex.Message}", ex);
                memoryItems = new List<MemoryItem>(); // 确保有一个空列表
                currentIndex = 0;
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"加载记忆内容时发生未知错误: {ex.Message}", ex);
                memoryItems = new List<MemoryItem>(); // 确保有一个空列表
                currentIndex = 0;
            }
        }

        /// <summary>
        /// 显示当前项目的内容
        /// </summary>
        /// <param name="autoRead">是否自动朗读</param>
        /// <param name="respectUserStopChoice">是否尊重用户的停止朗读选择</param>
        private void DisplayCurrentItem(bool autoRead = true, bool respectUserStopChoice = true)
        {
            try
            {
                // 通过名称查找控件，避免编译时引用问题
                var contentTextBlock = this.FindName("contentText") as System.Windows.Controls.TextBlock;

                if (contentTextBlock == null)
                {
                    Logger.Warning("无法找到contentText控件");
                    return;
                }

                if (!IsValidCurrentIndex())
                {
                    contentTextBlock.Text = "没有记忆内容。请通过设置导入内容。";
                    return;
                }

                contentTextBlock.Text = memoryItems[currentIndex].Content;

                // 根据参数决定是否自动朗读
                bool shouldAutoRead = autoRead && settings.AutoRead && (!respectUserStopChoice || !isReadingStopped);

                if (shouldAutoRead)
                {
                    ReadCurrentItem();
                }
                else
                {
                    // 确保朗读按钮状态正确
                    UpdateSpeakButtonState(false);

                    // 如果自动播放开启，启动计时器继续播放
                    // 这包括以下情况：
                    // 1. 用户主动停止了朗读 (isReadingStopped = true)
                    // 2. TTS不可用但自动播放开启
                    // 3. 自动朗读被关闭但自动播放开启
                    if (settings.AutoAdvance)
                    {
                        Logger.Info($"自动播放开启，启动间隔计时器。原因：AutoRead={settings.AutoRead}, isReadingStopped={isReadingStopped}, TTS可用={isTtsAvailable}");
                        intervalTimer.Start();
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"显示内容时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动显示当前项目（不自动朗读）
        /// </summary>
        private void DisplayCurrentItemManually()
        {
            DisplayCurrentItem(autoRead: false, respectUserStopChoice: false);
        }

        /// <summary>
        /// 显示当前项目并根据用户设置决定是否自动朗读
        /// </summary>
        private void DisplayCurrentItemWithAutoRead()
        {
            DisplayCurrentItem(autoRead: true, respectUserStopChoice: true);
        }

        private void ReadCurrentItem()
        {
            if (memoryItems.Count == 0 || currentIndex < 0 || currentIndex >= memoryItems.Count)
                return;

            // 如果用户已经停止了朗读，则不执行朗读操作
            if (isReadingStopped)
            {
                // 但如果自动播放开启，应该启动计时器继续播放
                if (settings.AutoAdvance)
                {
                    Logger.Info("用户停止了朗读，但自动播放开启，启动间隔计时器");
                    intervalTimer.Start();
                }
                return;
            }

            // 检查TTS是否可用
            if (!isTtsAvailable || ttsHelper == null || !ttsHelper.IsAvailable)
            {
                Logger.Warning("TTS功能不可用，跳过朗读");
                // TTS不可用时，如果自动播放开启，应该启动计时器继续播放
                if (settings.AutoAdvance)
                {
                    Logger.Info("TTS不可用，但自动播放开启，启动间隔计时器");
                    intervalTimer.Start();
                }
                return;
            }

            try
            {
                var text = memoryItems[currentIndex].Content;

                // 停止之前的朗读
                ttsHelper.Stop();
                isSpeaking = false;

                // 开始新的朗读
                bool success = ttsHelper.SpeakAsync(text);
                if (success)
                {
                    isSpeaking = true;
                    // 更新朗读按钮状态
                    UpdateSpeakButtonState(true);
                }
                else
                {
                    Logger.Warning("朗读启动失败");
                    UpdateSpeakButtonState(false);
                    // 朗读启动失败时，如果自动播放开启，应该启动计时器继续播放
                    if (settings.AutoAdvance)
                    {
                        Logger.Info("朗读启动失败，但自动播放开启，启动间隔计时器");
                        intervalTimer.Start();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("朗读时出错", ex);
                isSpeaking = false;
                UpdateSpeakButtonState(false);

                // 朗读出错时，如果自动播放开启，应该启动计时器继续播放
                if (settings.AutoAdvance)
                {
                    Logger.Info("朗读出错，但自动播放开启，启动间隔计时器");
                    intervalTimer.Start();
                }

                // TTS错误提示
                MessageBox.Show("语音朗读功能出现问题。\n\n建议：\n1. 检查系统音量设置\n2. 重启程序\n3. 重启计算机",
                    "朗读提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrevButton_Click(object sender, RoutedEventArgs e)
        {
            if (memoryItems.Count == 0)
                return;

            // 记录当前的播放状态
            bool wasAutoPlaying = IsAutoPlaying();

            // 停止间隔计时器和任何正在进行的朗读
            intervalTimer.Stop();
            ttsHelper?.Stop();
            isSpeaking = false;
            UpdateSpeakButtonState(false);

            // 重置重复计数器
            currentRepeatCount = 0;

            currentIndex = (currentIndex - 1 + memoryItems.Count) % memoryItems.Count;

            // 手动导航时显示内容，如果用户没有主动停止朗读，则自动朗读
            DisplayCurrentItemWithAutoRead();
            SaveProgress();

            // 如果之前在自动播放，恢复播放状态
            if (wasAutoPlaying && settings.AutoAdvance)
            {
                // 重新启动自动播放，但要考虑朗读状态
                if (isReadingStopped)
                {
                    // 如果朗读被停止，直接启动计时器
                    intervalTimer.Start();
                }
                // 如果朗读没有被停止，DisplayCurrentItemWithAutoRead已经处理了朗读
                // 朗读完成后会自动启动计时器
            }
        }

        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            if (memoryItems.Count == 0)
                return;

            // 记录当前的播放状态
            bool wasAutoPlaying = IsAutoPlaying();

            // 停止间隔计时器和任何正在进行的朗读
            intervalTimer.Stop();
            ttsHelper?.Stop();
            isSpeaking = false;
            UpdateSpeakButtonState(false);

            // 重置重复计数器
            currentRepeatCount = 0;

            currentIndex = (currentIndex + 1) % memoryItems.Count;

            // 手动导航时显示内容，如果用户没有主动停止朗读，则自动朗读
            DisplayCurrentItemWithAutoRead();
            SaveProgress();

            // 如果之前在自动播放，恢复播放状态
            if (wasAutoPlaying && settings.AutoAdvance)
            {
                // 重新启动自动播放，但要考虑朗读状态
                if (isReadingStopped)
                {
                    // 如果朗读被停止，直接启动计时器
                    intervalTimer.Start();
                }
                // 如果朗读没有被停止，DisplayCurrentItemWithAutoRead已经处理了朗读
                // 朗读完成后会自动启动计时器
            }
        }

        private void PlayButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查TTS是否可用
            if (!isTtsAvailable || ttsHelper == null || !ttsHelper.IsAvailable)
            {
                // 显示更友好的提示信息，并提供解决方案
                var result = MessageBox.Show(
                    "语音功能当前不可用，但您仍可以正常浏览记忆内容。\n\n" +
                    "是否要查看语音功能的详细诊断信息？\n\n" +
                    "点击\"是\"查看诊断信息\n" +
                    "点击\"否\"继续使用程序（静音模式）",
                    "语音功能提示",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    ShowTTSDiagnosticInfo();
                }
                return;
            }

            // 切换朗读状态
            if (isSpeaking)
            {
                // 当前正在朗读，停止朗读并标记为用户主动停止
                ttsHelper.Stop();
                isSpeaking = false;
                isReadingStopped = true; // 用户主动停止了朗读
                UpdateSpeakButtonState(false);
            }
            else
            {
                // 当前没有朗读，开始朗读并取消停止状态
                isReadingStopped = false; // 用户重新启用了朗读
                currentRepeatCount = 0; // 重置重复计数器
                ReadCurrentItem();
                // ReadCurrentItem() 内部已经调用了 UpdateSpeakButtonState(true)，无需重复调用
            }
        }

        private void UpdateSpeakButtonState(bool isPlaying)
        {
            try
            {
                // 通过名称查找控件，避免编译时引用问题
                var playBtn = this.FindName("playButton") as System.Windows.Controls.Button;
                var speakIconPath = this.FindName("speakIcon") as System.Windows.Shapes.Path;

                if (playBtn != null && speakIconPath != null)
                {
                    if (isPlaying)
                    {
                        playBtn.ToolTip = "停止朗读";
                        // 设置停止朗读图标
                        try
                        {
                            speakIconPath.Data = (PathGeometry)Application.Current.Resources["SpeakStopIcon"];
                        }
                        catch
                        {
                            // 如果图标资源不存在，使用默认图标
                            speakIconPath.Data = (PathGeometry)Application.Current.Resources["SpeakIcon"];
                        }
                    }
                    else
                    {
                        playBtn.ToolTip = "朗读";
                        // 设置朗读图标
                        speakIconPath.Data = (PathGeometry)Application.Current.Resources["SpeakIcon"];
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响程序运行
                Logger.Warning($"更新朗读按钮状态时出错: {ex.Message}");
            }
        }

        private void PlayPauseButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换播放/暂停状态
            if (IsAutoPlaying())
            {
                // 当前正在自动播放，暂停
                StopAutoPlay();
            }
            else
            {
                // 当前已暂停，开始自动播放
                StartAutoPlay();
            }
        }

        private bool IsAutoPlaying()
        {
            // 判断是否正在自动播放：
            // 1. 计时器正在运行（等待间隔时间）
            // 2. 设置了自动前进且正在朗读（朗读完成后会启动计时器）
            // 注意：即使用户停止了朗读，只要设置了自动前进，就应该被认为是在播放状态
            return intervalTimer.IsEnabled ||
                   (settings.AutoAdvance && isSpeaking) ||
                   settings.AutoAdvance; // 简化逻辑：只要设置了自动前进就是播放状态
        }

        private void StartAutoPlay()
        {
            settings.AutoAdvance = true;
            UpdatePlayPauseButtonState(true);

            // 如果没有开启自动朗读，立即启动间隔计时器
            if (!settings.AutoRead)
            {
                intervalTimer.Start();
            }
            else
            {
                // 如果开启了自动朗读，且当前没有在朗读，且用户没有停止朗读，则开始朗读
                if (!isSpeaking && !isReadingStopped)
                {
                    ReadCurrentItem();
                }
                // 如果用户停止了朗读，直接启动计时器进行无朗读播放
                else if (isReadingStopped)
                {
                    intervalTimer.Start();
                }
                // 如果正在朗读，等朗读完成后计时器会自动启动
            }
        }

        private void StopAutoPlay()
        {
            settings.AutoAdvance = false;
            intervalTimer.Stop();
            UpdatePlayPauseButtonState(false);

            // 注意：这里不停止朗读，朗读状态由朗读/停止按钮独立控制
            // 用户可能希望停止自动播放但继续听当前内容的朗读
        }

        private void EasyButton_Click(object sender, RoutedEventArgs e)
        {
            HandleDifficultyReview(Difficulty.Easy);
        }

        private void HardButton_Click(object sender, RoutedEventArgs e)
        {
            HandleDifficultyReview(Difficulty.Hard);
        }

        /// <summary>
        /// 处理难度评价的通用方法
        /// </summary>
        /// <param name="difficulty">难度级别</param>
        private void HandleDifficultyReview(Difficulty difficulty)
        {
            if (!IsValidCurrentIndex())
                return;

            try
            {
                var currentItem = memoryItems[currentIndex];

                // 使用选定的算法标记为已复习
                currentItem.MarkAsReviewed(difficulty, settings.SelectedAlgorithm);

                // 更新统计信息
                UpdateReviewStatistics(currentItem);

                // 延迟保存数据以提高性能
                ScheduleDataSave();

                AutoAdvanceToNext();
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"处理难度评价时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查当前索引是否有效
        /// </summary>
        /// <returns>索引是否有效</returns>
        private bool IsValidCurrentIndex()
        {
            return memoryItems.Count > 0 && currentIndex >= 0 && currentIndex < memoryItems.Count;
        }

        /// <summary>
        /// 更新复习统计信息
        /// </summary>
        /// <param name="currentItem">当前项目</param>
        private void UpdateReviewStatistics(MemoryItem currentItem)
        {
            settings.TodayReviewItemsStudied++;
            bool isNewItem = currentItem.ReviewCount <= 1;
            settings.UpdateStudyStats(currentItem.TotalStudyTime, isNewItem);
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Info("打开设置对话框");

                // 检查操作系统版本
                var osVersion = Environment.OSVersion;
                bool isWindows7 = osVersion.Version.Major == 6 && osVersion.Version.Minor == 1;
                Logger.Info($"当前系统: {osVersion.VersionString}, Windows 7: {isWindows7}");

                // 记录当前播放状态和进度
                bool wasAutoPlaying = IsAutoPlaying();
                int previousIndex = currentIndex; // 保存当前播放进度

                // 停止计时器和朗读
                intervalTimer.Stop();
                ttsHelper?.Stop();
                isSpeaking = false;
                UpdateSpeakButtonState(false);

                // 创建设置对话框
                SettingsDialog? settingsDialog = null;
                try
                {
                    Logger.Info("开始创建设置对话框");
                    settingsDialog = new SettingsDialog(settings);
                    Logger.Info("设置对话框创建成功");

                    // 设置对话框属性以确保在Windows 7上正确显示
                    settingsDialog.Owner = this;

                    if (isWindows7)
                    {
                        // Windows 7特定设置
                        settingsDialog.WindowStartupLocation = WindowStartupLocation.CenterOwner;
                        settingsDialog.ShowInTaskbar = false;
                        settingsDialog.Topmost = false; // 避免在Windows 7上的显示问题
                        settingsDialog.ShowActivated = true;
                        settingsDialog.Focusable = true;

                        // 确保对话框在屏幕范围内
                        var workingArea = SystemParameters.WorkArea;
                        if (settingsDialog.Width > workingArea.Width - 100)
                        {
                            settingsDialog.Width = workingArea.Width - 100;
                        }
                        if (settingsDialog.Height > workingArea.Height - 100)
                        {
                            settingsDialog.Height = workingArea.Height - 100;
                        }

                        Logger.Info("Windows 7兼容性设置已应用到设置对话框");
                    }
                    else
                    {
                        // 非Windows 7系统的标准设置
                        settingsDialog.WindowStartupLocation = WindowStartupLocation.CenterOwner;
                        settingsDialog.ShowInTaskbar = false;
                    }

                    Logger.Info("准备显示设置对话框");

                    // 显示对话框
                    var dialogResult = settingsDialog.ShowDialog();
                    Logger.Info($"设置对话框关闭，结果: {dialogResult}");

                    if (dialogResult == true && settingsDialog.SettingsChanged)
                    {
                        Logger.Info("用户确认设置更改，开始应用新设置");

                        settings = settingsDialog.Settings;
                        SettingsManager.SaveSettings(settings);
                        ApplySettings();

                        // 重新加载记忆项目（可能有新导入的内容）
                        var previousItemsCount = memoryItems.Count;
                        LoadMemoryItems();

                        // 保持播放进度：只有在文件组改变或内容完全不同时才重置
                        if (memoryItems.Count > 0)
                        {
                            // 如果内容数量没有变化，保持之前的进度
                            if (memoryItems.Count == previousItemsCount && previousIndex < memoryItems.Count)
                            {
                                currentIndex = previousIndex; // 保持之前的播放进度
                            }
                            // 如果内容有变化，但之前的索引仍然有效，保持进度
                            else if (previousIndex < memoryItems.Count)
                            {
                                currentIndex = previousIndex; // 保持之前的播放进度
                            }
                            // 只有在之前的索引超出范围时才重置到第一项
                            else
                            {
                                currentIndex = 0;
                            }
                            SaveProgress();
                        }

                        // 显示当前内容，但不自动开始播放
                        DisplayCurrentItemManually();

                        // 如果用户之前在自动播放且设置仍然允许自动播放，则恢复播放状态
                        if (wasAutoPlaying && settings.AutoAdvance)
                        {
                            // 更新按钮状态为播放状态，但不立即开始播放
                            // 用户需要手动点击播放按钮来重新开始
                            UpdatePlayPauseButtonState(true);
                            settings.AutoAdvance = true; // 确保设置正确
                        }

                        Logger.Info("设置应用完成");
                    }
                    else
                    {
                        Logger.Info("用户取消设置或无更改，恢复之前状态");

                        // 用户取消了设置，恢复之前的播放状态
                        if (wasAutoPlaying && settings.AutoAdvance)
                        {
                            StartAutoPlay();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error("创建或显示设置对话框时出错", ex);

                    // 根据系统版本显示不同的错误消息
                    string errorMessage;
                    if (isWindows7)
                    {
                        errorMessage = "无法打开设置对话框。\n\n" +
                                     $"错误信息：{ex.Message}\n\n" +
                                     "Windows 7 专用解决方案：\n" +
                                     "1. 以管理员身份运行程序\n" +
                                     "2. 检查 .NET Framework 版本\n" +
                                     "3. 重启程序\n" +
                                     "4. 运行 Windows7-Diagnostic.bat 诊断\n" +
                                     "5. 重启计算机";
                    }
                    else
                    {
                        errorMessage = "无法打开设置对话框。\n\n" +
                                     $"错误信息：{ex.Message}\n\n" +
                                     "建议解决方案：\n" +
                                     "1. 重启程序\n" +
                                     "2. 检查系统资源\n" +
                                     "3. 以管理员身份运行程序";
                    }

                    MessageBox.Show(errorMessage, "设置对话框错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);

                    // 恢复之前的播放状态
                    if (wasAutoPlaying && settings.AutoAdvance)
                    {
                        StartAutoPlay();
                    }
                }
                finally
                {
                    // 确保对话框被正确释放
                    try
                    {
                        settingsDialog?.Close();
                        settingsDialog = null;
                        Logger.Info("设置对话框资源已释放");
                    }
                    catch (Exception disposeEx)
                    {
                        Logger.Warning($"释放设置对话框资源时出错: {disposeEx.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("设置按钮点击处理出错", ex);

                MessageBox.Show($"处理设置请求时发生错误：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        /// <summary>
        /// 处理键盘快捷键
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">键盘事件参数</param>
        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // 检查是否有修饰键
                bool ctrlPressed = (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control;

                switch (e.Key)
                {
                    case Key.Left:
                    case Key.A:
                        // 左箭头或A键：上一条
                        PrevButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：上一条");
                        break;

                    case Key.Right:
                    case Key.D:
                        // 右箭头或D键：下一条
                        NextButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：下一条");
                        break;

                    case Key.Space:
                        // 空格键：播放/暂停
                        PlayPauseButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：播放/暂停");
                        break;

                    case Key.R:
                        // R键：朗读/停止朗读
                        PlayButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：朗读/停止朗读");
                        break;

                    case Key.E:
                        // E键：标记为简单
                        EasyButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：标记为简单");
                        break;

                    case Key.H:
                        // H键：标记为困难
                        HardButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：标记为困难");
                        break;

                    case Key.S:
                        if (ctrlPressed)
                        {
                            // Ctrl+S：立即保存
                            SaveAllData();
                            e.Handled = true;
                            Logger.Info("键盘快捷键：立即保存");
                        }
                        else
                        {
                            // S键：打开设置
                            SettingsButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            Logger.Info("键盘快捷键：打开设置");
                        }
                        break;

                    case Key.Escape:
                        // ESC键：关闭程序
                        CloseButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：关闭程序");
                        break;

                    case Key.F1:
                        // F1键：显示帮助
                        ShowKeyboardShortcutsHelp();
                        e.Handled = true;
                        break;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("处理键盘快捷键时出错", ex);
            }
        }

        /// <summary>
        /// 显示键盘快捷键帮助
        /// </summary>
        private void ShowKeyboardShortcutsHelp()
        {
            var helpMessage = @"键盘快捷键：

导航：
← / A    上一条内容
→ / D    下一条内容

控制：
空格键    播放/暂停自动播放
R        朗读/停止朗读当前内容

评价：
E        标记为简单
H        标记为困难

其他：
S        打开设置
Ctrl+S   立即保存数据
ESC      关闭程序
F1       显示此帮助";

            MessageBox.Show(helpMessage, "键盘快捷键帮助", MessageBoxButton.OK, MessageBoxImage.Information);
            Logger.Info("显示键盘快捷键帮助");
        }



        private void SaveProgress()
        {
            // 如果选择了文件组，保存文件组进度；否则保存旧的统一进度
            if (!string.IsNullOrEmpty(settings.CurrentFileGroupId))
            {
                MemoryManager.SaveFileGroupProgress(settings.CurrentFileGroupId, currentIndex);
            }
            else
            {
                MemoryManager.SaveProgress(currentIndex);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // 停止所有计时器
                intervalTimer?.Stop();
                saveTimer?.Stop();

                // 结束所有项目的学习会话
                foreach (var item in memoryItems)
                {
                    item.EndStudySession();
                }

                // 立即保存所有数据
                SaveProgress();
                SaveAllData();

                // 释放TTS助手资源
                ttsHelper?.Dispose();
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止程序关闭
                System.Diagnostics.Debug.WriteLine($"关闭程序时出错: {ex.Message}");
            }
            finally
            {
                base.OnClosed(e);
            }
        }
    }
}