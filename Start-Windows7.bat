@echo off
title Memory Enhancer - Windows 7 Launcher

echo ========================================
echo Memory Enhancer - Windows 7 Launcher
echo ========================================
echo Version: 1.0.1 Windows 7 Enhanced
echo.

:: Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Detected Windows version: %VERSION%
if "%VERSION%"=="6.1" (
    echo [OK] Windows 7 detected
) else (
    echo [WARNING] This launcher is optimized for Windows 7
    echo Your system version: %VERSION%
)
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [INFO] Program is not running as administrator
    echo [TIP] If you encounter speech function issues, please:
    echo       1. Right-click this file and select "Run as administrator"
    echo       2. Or run Windows7-Diagnostic.bat for detailed diagnosis
    echo.
) else (
    echo [OK] Running with administrator privileges
    echo.
)

:: Check if program file exists
set "PROGRAM_PATH=MemoryEnhancer.exe"
if exist "bin\Release\netcoreapp3.1\win7-x64\MemoryEnhancer.exe" (
    set "PROGRAM_PATH=bin\Release\netcoreapp3.1\win7-x64\MemoryEnhancer.exe"
    echo [OK] Found compiled program: %PROGRAM_PATH%
) else if exist "MemoryEnhancer.exe" (
    echo [OK] Found program: %PROGRAM_PATH%
) else (
    echo [ERROR] Cannot find main program file MemoryEnhancer.exe
    echo Please ensure the program is compiled or files are in the same directory
    echo.
    echo Current directory: %CD%
    echo Searching for program files:
    dir /b *.exe 2>nul
    if exist "bin" (
        echo.
        echo Checking bin directory:
        dir /b /s bin\*.exe 2>nul
    )
    echo.
    pause
    exit /b 1
)

:: Check critical dependency files
echo Checking program files...
set "missing_files="
set "critical_files=System.Speech.dll EPPlus.dll DocumentFormat.OpenXml.dll"

:: Determine the directory to check based on program path
set "CHECK_DIR=."
if "%PROGRAM_PATH%"=="bin\Release\netcoreapp3.1\win7-x64\MemoryEnhancer.exe" (
    set "CHECK_DIR=bin\Release\netcoreapp3.1\win7-x64"
)

echo Checking dependencies in: %CHECK_DIR%

for %%f in (%critical_files%) do (
    if not exist "%CHECK_DIR%\%%f" (
        set "missing_files=!missing_files! %%f"
        echo [ERROR] Missing: %%f
    ) else (
        echo [OK] Found: %%f
    )
)

if not "%missing_files%"=="" (
    echo.
    echo [WARNING] Missing critical files:%missing_files%
    echo Program may not run properly
    echo.
    echo Suggested actions:
    echo 1. Re-download the complete Windows 7 package
    echo 2. Check if antivirus software quarantined files
    echo 3. Run Windows7-Diagnostic.bat for detailed check
    echo 4. Rebuild the project: dotnet build --configuration Release
    echo.
) else (
    echo [OK] All critical dependencies found
)

:: Check Windows Audio service
echo Checking Windows Audio service...
sc query AudioSrv | find "RUNNING" >nul
if %errorlevel% neq 0 (
    echo Warning: Windows Audio service is not running
    echo Attempting to start service...
    net start AudioSrv >nul 2>&1
    if %errorlevel% equ 0 (
        echo [OK] Windows Audio service started
    ) else (
        echo [ERROR] Cannot start Windows Audio service, speech function may not be available
    )
) else (
    echo [OK] Windows Audio service is running
)

:: Check TTS components (Windows 7 specific)
echo Checking TTS components...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] TTS components not detected
    echo Windows 7 requires additional TTS components for speech function
    echo.
    echo Available TTS installation tools:
    if exist "Windows7-TTS-AutoInstaller.bat" echo   1. Windows7-TTS-AutoInstaller.bat  (Auto download & install)
    if exist "Create-TTS-Package.bat" echo   2. Create-TTS-Package.bat          (Create offline package)
    if exist "Windows7-TTS-Setup.bat" echo   3. Windows7-TTS-Setup.bat          (Installation guide)
    echo.
    echo The program will run in silent mode without TTS components.
    echo To install TTS components, run one of the above tools after program startup.
    echo.
    set /p tts_install="Install TTS components now? (Y/N): "
    if /i "!tts_install!"=="Y" (
        if exist "Windows7-TTS-AutoInstaller.bat" (
            echo Starting TTS auto-installer...
            start "" "Windows7-TTS-AutoInstaller.bat"
            echo.
            echo TTS installer started. Please complete the installation and restart this program.
            pause
            exit /b 0
        ) else (
            echo Auto-installer not found. Please run TTS installation tools manually.
        )
    )
) else (
    for /f %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^| find /c "HKEY"') do set voice_count=%%i
    echo [OK] TTS components detected (Voices: !voice_count!)
)
echo.

:: Set environment variables to optimize performance
echo Optimizing runtime environment...
set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
set DOTNET_EnableWriteXorExecute=0

:: Create log directory
if not exist "%APPDATA%\MemoryEnhancer\Logs" (
    mkdir "%APPDATA%\MemoryEnhancer\Logs" >nul 2>&1
)

echo Starting program...
echo.

:: Start program and capture errors
"%PROGRAM_PATH%" 2>error.log
set "exit_code=%errorlevel%"

:: Check for errors
if %exit_code% neq 0 (
    echo.
    echo ========================================
    echo Program exited abnormally (Error code: %exit_code%)
    echo ========================================
    echo.

    if exist "error.log" (
        echo [ERROR] Error information found:
        echo ----------------------------------------
        type "error.log"
        echo ----------------------------------------
        echo.
    )

    echo Windows 7 specific troubleshooting:
    echo.
    echo [STEP 1] Basic solutions:
    echo   1. Restart computer
    echo   2. Run program as administrator
    echo   3. Check antivirus software settings
    echo.
    echo [STEP 2] Windows 7 specific solutions:
    echo   4. Install Microsoft Speech Platform Runtime 11.0
    echo   5. Install Chinese TTS voice pack
    echo   6. Check Windows Audio service status
    echo   7. Update .NET Framework to 4.0 or higher
    echo.
    echo [STEP 3] Advanced solutions:
    echo   8. Run Windows7-Diagnostic.bat for detailed diagnosis
    echo   9. Re-download complete Windows 7 package
    echo   10. Check Windows Update for system patches
    echo.

    choice /c YN /m "Run Windows 7 diagnostic tool now? (Y/N)"
    if %errorlevel% equ 1 (
        if exist "Windows7-Diagnostic.bat" (
            echo.
            echo Running Windows 7 diagnostic tool...
            call "Windows7-Diagnostic.bat"
        ) else (
            echo [ERROR] Windows7-Diagnostic.bat not found
            echo Please ensure you have the complete Windows 7 package
        )
    )
)

:: Clean up temporary files
if exist "error.log" del "error.log" >nul 2>&1

echo.
echo Program has exited
pause
