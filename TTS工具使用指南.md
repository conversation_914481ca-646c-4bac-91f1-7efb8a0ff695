# Windows 7 TTS 工具完整使用指南

## 🎯 工具概览

为了解决Windows 7系统上"我要记忆"程序的TTS语音功能问题，我们提供了完整的工具套件：

### 📥 下载工具（3个）
| 工具名称 | 特点 | 适用场景 |
|---------|------|---------|
| `一键下载TTS组件.bat` | 简单快速，一键下载 | 新手用户，快速下载 |
| `下载TTS组件.bat` | 功能完整，选项丰富 | 高级用户，自定义下载 |
| `下载TTS组件.ps1` | 进度显示，错误处理好 | 需要详细进度的用户 |

### 🛠️ 安装工具（3个）
| 工具名称 | 特点 | 适用场景 |
|---------|------|---------|
| `Windows7-TTS-Complete-Installer.bat` | 最新版，功能最全 | 推荐使用，一站式解决 |
| `Windows7-TTS-AutoInstaller.bat` | 经典版，稳定可靠 | 网络环境好的用户 |
| `Windows7-TTS-FixTool.bat` | 专门修复问题 | 安装失败或功能异常 |

### 🔧 诊断工具（2个）
| 工具名称 | 特点 | 适用场景 |
|---------|------|---------|
| `Windows7-Diagnostic.bat` | 全面系统诊断 | 问题排查，状态检查 |
| `Windows7-TTS-Setup.bat` | 安装指导 | 获取安装说明 |

### 📁 管理工具（2个）
| 工具名称 | 特点 | 适用场景 |
|---------|------|---------|
| `TTS组件管理器.bat` | 统一管理界面 | 不知道用哪个工具时 |
| `Create-TTS-Package.bat` | 创建离线包 | 无网络环境部署 |

## 🚀 快速开始

### 方案一：最简单（推荐新手）
1. 双击运行 `一键下载TTS组件.bat`
2. 等待下载完成
3. 进入"TTS组件"文件夹
4. 右键点击"快速安装.bat"，选择"以管理员身份运行"
5. 重启计算机

### 方案二：一站式解决（推荐）
1. 右键点击 `Windows7-TTS-Complete-Installer.bat`
2. 选择"以管理员身份运行"
3. 按照提示完成安装
4. 重启计算机

### 方案三：使用管理器
1. 双击运行 `TTS组件管理器.bat`
2. 根据菜单选择相应功能
3. 按照提示操作

## 📋 详细使用说明

### 下载工具使用

#### 一键下载TTS组件.bat
```bash
# 特点：最简单，适合新手
# 使用：双击运行即可
# 输出：在"TTS组件"文件夹中生成所有必需文件
```

**使用步骤：**
1. 双击运行文件
2. 确认下载（按Y）
3. 等待下载完成
4. 查看"TTS组件"文件夹

#### 下载TTS组件.bat
```bash
# 特点：功能完整，选项丰富
# 使用：双击运行，可自定义选择
# 输出：可选择下载哪些组件
```

**使用步骤：**
1. 双击运行文件
2. 选择下载选项（1-4）
3. 等待下载完成
4. 使用生成的安装脚本

#### 下载TTS组件.ps1
```powershell
# 特点：PowerShell版本，进度显示好
# 使用：右键"用PowerShell运行"
# 输出：详细的下载进度和错误信息
```

**使用步骤：**
1. 右键点击文件
2. 选择"用PowerShell运行"
3. 观察下载进度
4. 使用生成的安装脚本

### 安装工具使用

#### Windows7-TTS-Complete-Installer.bat（推荐）
```bash
# 特点：最新版本，功能最全面
# 功能：诊断 + 下载 + 安装 + 验证
# 要求：管理员权限
```

**使用步骤：**
1. 右键点击文件
2. 选择"以管理员身份运行"
3. 等待系统诊断
4. 按提示下载和安装
5. 选择重启计算机

#### Windows7-TTS-FixTool.bat
```bash
# 特点：专门修复TTS问题
# 功能：诊断 + 修复 + 测试
# 适用：安装失败或功能异常
```

**使用步骤：**
1. 右键点击文件
2. 选择"以管理员身份运行"
3. 查看诊断结果
4. 按提示执行修复
5. 测试TTS功能

### 诊断工具使用

#### Windows7-Diagnostic.bat
```bash
# 特点：全面的系统诊断
# 功能：检查系统状态、服务、组件
# 输出：详细的诊断报告
```

**诊断内容：**
- 操作系统版本
- .NET Framework版本
- Windows Audio服务状态
- SAPI组件状态
- 语音包安装情况
- 程序文件完整性

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 下载失败
**症状：** 下载过程中出现错误
**解决：**
- 检查网络连接
- 尝试使用不同的下载工具
- 手动访问Microsoft官网下载

#### 2. 安装失败
**症状：** 安装过程中出现错误代码
**解决：**
- 确保以管理员身份运行
- 临时禁用防病毒软件
- 检查磁盘空间是否充足
- 运行修复工具

#### 3. 安装后无效果
**症状：** 安装完成但程序仍提示TTS不可用
**解决：**
- 确认已重启计算机
- 检查Windows Audio服务
- 运行诊断工具检查状态
- 重新安装组件

#### 4. 权限问题
**症状：** 提示需要管理员权限
**解决：**
- 右键选择"以管理员身份运行"
- 检查UAC设置
- 确保当前用户有管理员权限

### 诊断命令

```batch
# 检查语音包
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens"

# 检查Windows Audio服务
sc query AudioSrv

# 测试TTS功能
echo Set objVoice = CreateObject("SAPI.SpVoice") > test.vbs
echo objVoice.Speak "测试" >> test.vbs
cscript test.vbs
del test.vbs
```

## 📁 文件结构

下载完成后的文件结构：
```
TTS组件/
├── SpeechPlatformRuntime.msi          # 语音平台运行时
├── MSSpeech_TTS_zh-CN_HuiHui.msi      # 中文女声语音包
├── MSSpeech_TTS_zh-CN_Kangkang.msi    # 中文男声语音包
├── 安装TTS组件.bat                     # 自动安装脚本
├── 快速安装.bat                        # 简化安装脚本
└── 使用说明.txt                        # 说明文档
```

## ⚙️ 系统要求

- **操作系统：** Windows 7 SP1 或更高版本
- **框架：** .NET Framework 4.0 或更高版本
- **权限：** 管理员权限（安装时）
- **磁盘空间：** 至少200MB可用空间
- **网络：** 稳定的网络连接（下载时）

## 🎉 验证安装成功

安装成功的标志：
1. 程序启动时不显示"TTS功能不可用"错误
2. 点击朗读按钮能听到语音
3. 重复播放次数设置生效
4. 控制面板中能看到已安装的语音

验证步骤：
1. 重启计算机
2. 运行"我要记忆"程序
3. 测试语音朗读功能
4. 检查重复播放功能

## 📞 技术支持

如果遇到问题：
1. 查看 `Windows7-TTS-快速解决方案.md`
2. 运行 `Windows7-Diagnostic.bat` 诊断
3. 查看程序日志：`%APPDATA%\MemoryEnhancer\Logs\`
4. 检查Windows事件日志

## 🔄 更新说明

工具版本历史：
- v1.0：基础下载和安装功能
- v2.0：增加完整安装器和修复工具
- v2.1：添加PowerShell版本和管理器

---

**注意：** 所有工具都经过测试，但由于系统环境差异，如果遇到问题请参考故障排除部分或使用诊断工具。
