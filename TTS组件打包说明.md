# Windows 7 TTS 组件打包说明

## 概述

为了解决 Windows 7 系统缺少 TTS（文本到语音）功能的问题，我创建了自动化的打包和安装工具。

## 方案一：自动下载安装（推荐）

### 使用 Windows7-TTS-AutoInstaller.bat

这个脚本会自动下载并安装所需组件：

1. **运行脚本**
   ```
   右键点击 Windows7-TTS-AutoInstaller.bat
   选择"以管理员身份运行"
   ```

2. **自动流程**
   - 自动下载 Microsoft Speech Platform Runtime 11.0
   - 自动下载中文语音包（慧慧/康康）
   - 提供安装向导
   - 提示重启计算机

3. **优点**
   - 完全自动化
   - 实时下载最新版本
   - 包含安装验证

## 方案二：创建离线安装包

### 使用 Create-TTS-Package.bat

如果需要在没有网络的环境中安装，可以预先创建离线包：

1. **在有网络的计算机上运行**
   ```
   右键点击 Create-TTS-Package.bat
   选择"以管理员身份运行"
   ```

2. **自动创建离线包**
   - 下载所有必需组件到 `TTS_Components` 文件夹
   - 创建 `Install-TTS.bat` 离线安装脚本
   - 生成 `README.txt` 说明文件

3. **使用离线包**
   - 将 `TTS_Components` 文件夹复制到目标计算机
   - 在目标计算机上以管理员身份运行 `Install-TTS.bat`

## 包含的组件

### 1. Microsoft Speech Platform Runtime 11.0 (x64)
- **文件名**: `SpeechPlatformRuntime.msi`
- **大小**: 约 5MB
- **作用**: 语音平台核心运行时
- **必需**: 是

### 2. 中文语音包 - 慧慧（女声）
- **文件名**: `MSSpeech_TTS_zh-CN_HuiHui.msi`
- **大小**: 约 60MB
- **作用**: 中文女声语音合成
- **推荐**: 是

### 3. 中文语音包 - 康康（男声）
- **文件名**: `MSSpeech_TTS_zh-CN_Kangkang.msi`
- **大小**: 约 60MB
- **作用**: 中文男声语音合成
- **可选**: 是

## 安装顺序

**重要**: 必须按以下顺序安装：

1. **先安装** Speech Platform Runtime
2. **再安装** 语音包（一个或多个）
3. **最后** 重启计算机

## 验证安装

安装完成并重启后，可以通过以下方式验证：

### 方法一：控制面板验证
1. 控制面板 → 语音识别
2. 点击"文本到语音转换"选项卡
3. 在"语音选择"下拉菜单中应该能看到已安装的语音
4. 点击"预览"按钮测试

### 方法二：程序验证
1. 重新运行"我要记忆"程序
2. 如果安装成功，程序启动时不会显示 TTS 错误
3. 语音功能和重复播放功能应该正常工作

### 方法三：诊断工具验证
```
运行 Windows7-Diagnostic.bat
查看"Voice Pack Check"部分
应该显示"已安装语音包数量: 1"或更多
```

## 故障排除

### 下载失败
- 检查网络连接
- 尝试使用不同的网络环境
- 手动访问 Microsoft 官网下载

### 安装失败
- 确保以管理员身份运行
- 检查磁盘空间是否充足
- 临时禁用防病毒软件
- 检查 Windows 更新

### 安装后无效果
- 确认已重启计算机
- 检查 Windows Audio 服务是否启动
- 运行 `Windows7-Diagnostic.bat` 诊断

## 技术细节

### 下载源
所有组件均从 Microsoft 官方服务器下载：
- Runtime: `https://download.microsoft.com/download/3/4/9/...`
- 语音包: `https://download.microsoft.com/download/6/9/3/...`

### 安装方式
使用 Windows Installer (msiexec) 静默安装：
```batch
msiexec /i "组件文件.msi" /quiet /norestart
```

### 兼容性
- 专为 Windows 7 x64 设计
- 需要 .NET Framework 4.0 或更高版本
- 需要管理员权限

## 文件清单

创建的工具文件：
- `Windows7-TTS-AutoInstaller.bat` - 自动下载安装器
- `Create-TTS-Package.bat` - 离线包创建工具
- `Windows7-TTS-Setup.bat` - 安装指导工具
- `Windows7-TTS-安装说明.txt` - 快速参考
- `TTS组件打包说明.md` - 本文档

## 使用建议

1. **有网络环境**: 使用 `Windows7-TTS-AutoInstaller.bat`
2. **无网络环境**: 先用 `Create-TTS-Package.bat` 创建离线包
3. **批量部署**: 创建离线包后分发到多台计算机
4. **故障诊断**: 使用 `Windows7-Diagnostic.bat` 和相关文档

## 注意事项

1. **管理员权限**: 所有安装操作都需要管理员权限
2. **重启必需**: 安装完成后必须重启计算机
3. **防病毒软件**: 可能需要临时禁用或添加白名单
4. **磁盘空间**: 确保有至少 200MB 可用空间
5. **网络要求**: 自动下载需要稳定的网络连接

通过这些工具，Windows 7 用户可以轻松安装 TTS 功能，让"我要记忆"程序的语音和重复播放功能正常工作。
