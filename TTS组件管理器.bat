@echo off
setlocal enabledelayedexpansion

title TTS组件管理器

echo ========================================
echo TTS 组件管理器 v1.0
echo ========================================
echo Windows 7 TTS语音功能一站式解决方案
echo.

:: 检查操作系统版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
if "%VERSION%"=="6.1" (
    echo [✓] Windows 7 系统确认
) else (
    echo [!] 当前系统: Windows %VERSION%
    echo [提示] 此工具主要为Windows 7设计
)
echo.

:main_menu
echo ========================================
echo 主菜单 - 请选择操作
echo ========================================
echo.
echo 📥 下载工具：
echo   1. 一键下载TTS组件 (推荐)
echo   2. 高级下载工具 (更多选项)
echo   3. PowerShell下载工具 (进度显示)
echo.
echo 🛠️ 安装工具：
echo   4. 完整自动安装器 (下载+安装)
echo   5. 自动安装器 (经典版)
echo   6. 问题修复工具
echo.
echo 🔧 诊断工具：
echo   7. 系统诊断
echo   8. TTS安装指导
echo   9. 查看快速解决方案
echo.
echo 📁 文件管理：
echo   A. 打开TTS组件文件夹
echo   B. 创建离线安装包
echo.
echo   0. 退出程序
echo.

set /p choice="请输入选择 (1-9, A-B, 0): "

if "%choice%"=="1" goto download_simple
if "%choice%"=="2" goto download_advanced
if "%choice%"=="3" goto download_powershell
if "%choice%"=="4" goto install_complete
if "%choice%"=="5" goto install_auto
if "%choice%"=="6" goto fix_tool
if "%choice%"=="7" goto diagnostic
if "%choice%"=="8" goto setup_guide
if "%choice%"=="9" goto quick_solution
if /i "%choice%"=="A" goto open_folder
if /i "%choice%"=="B" goto create_package
if "%choice%"=="0" goto exit_program

echo [错误] 无效选择，请重新输入
echo.
goto main_menu

:download_simple
echo.
echo ========================================
echo 启动一键下载工具
echo ========================================
if exist "一键下载TTS组件.bat" (
    echo 正在启动一键下载工具...
    call "一键下载TTS组件.bat"
) else (
    echo [错误] 找不到 "一键下载TTS组件.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:download_advanced
echo.
echo ========================================
echo 启动高级下载工具
echo ========================================
if exist "下载TTS组件.bat" (
    echo 正在启动高级下载工具...
    call "下载TTS组件.bat"
) else (
    echo [错误] 找不到 "下载TTS组件.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:download_powershell
echo.
echo ========================================
echo 启动PowerShell下载工具
echo ========================================
if exist "下载TTS组件.ps1" (
    echo 正在启动PowerShell下载工具...
    echo 注意：可能需要修改PowerShell执行策略
    powershell -ExecutionPolicy Bypass -File "下载TTS组件.ps1"
) else (
    echo [错误] 找不到 "下载TTS组件.ps1"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:install_complete
echo.
echo ========================================
echo 启动完整自动安装器
echo ========================================
if exist "Windows7-TTS-Complete-Installer.bat" (
    echo 正在启动完整安装器...
    echo 注意：需要管理员权限
    call "Windows7-TTS-Complete-Installer.bat"
) else (
    echo [错误] 找不到 "Windows7-TTS-Complete-Installer.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:install_auto
echo.
echo ========================================
echo 启动自动安装器
echo ========================================
if exist "Windows7-TTS-AutoInstaller.bat" (
    echo 正在启动自动安装器...
    echo 注意：需要管理员权限
    call "Windows7-TTS-AutoInstaller.bat"
) else (
    echo [错误] 找不到 "Windows7-TTS-AutoInstaller.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:fix_tool
echo.
echo ========================================
echo 启动问题修复工具
echo ========================================
if exist "Windows7-TTS-FixTool.bat" (
    echo 正在启动修复工具...
    echo 注意：需要管理员权限
    call "Windows7-TTS-FixTool.bat"
) else (
    echo [错误] 找不到 "Windows7-TTS-FixTool.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:diagnostic
echo.
echo ========================================
echo 启动系统诊断工具
echo ========================================
if exist "Windows7-Diagnostic.bat" (
    echo 正在启动诊断工具...
    call "Windows7-Diagnostic.bat"
) else (
    echo [错误] 找不到 "Windows7-Diagnostic.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:setup_guide
echo.
echo ========================================
echo 启动TTS安装指导
echo ========================================
if exist "Windows7-TTS-Setup.bat" (
    echo 正在启动安装指导...
    call "Windows7-TTS-Setup.bat"
) else (
    echo [错误] 找不到 "Windows7-TTS-Setup.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:quick_solution
echo.
echo ========================================
echo 查看快速解决方案
echo ========================================
if exist "Windows7-TTS-快速解决方案.md" (
    echo 正在打开快速解决方案文档...
    start "" "Windows7-TTS-快速解决方案.md"
) else (
    echo [错误] 找不到 "Windows7-TTS-快速解决方案.md"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:open_folder
echo.
echo ========================================
echo 打开TTS组件文件夹
echo ========================================

set "TTS_FOLDER="
if exist "TTS组件" (
    set "TTS_FOLDER=TTS组件"
) else if exist "TTS_Components" (
    set "TTS_FOLDER=TTS_Components"
)

if not "%TTS_FOLDER%"=="" (
    echo 正在打开文件夹: %TTS_FOLDER%
    explorer "%TTS_FOLDER%"
) else (
    echo [提示] TTS组件文件夹不存在
    echo 请先运行下载工具下载组件
    echo.
    set /p create_folder="是否现在创建TTS组件文件夹? (Y/N): "
    if /i "!create_folder!"=="Y" (
        mkdir "TTS组件" 2>nul
        echo [✓] 已创建 "TTS组件" 文件夹
        explorer "TTS组件"
    )
)
echo.
pause
goto main_menu

:create_package
echo.
echo ========================================
echo 创建离线安装包
echo ========================================
if exist "Create-TTS-Package.bat" (
    echo 正在启动离线包创建工具...
    call "Create-TTS-Package.bat"
) else (
    echo [错误] 找不到 "Create-TTS-Package.bat"
    echo 请确保文件存在于当前目录
)
echo.
pause
goto main_menu

:exit_program
echo.
echo ========================================
echo 退出程序
echo ========================================
echo.
echo 感谢使用TTS组件管理器！
echo.
echo 如果您在使用过程中遇到问题：
echo 1. 查看 "Windows7-TTS-快速解决方案.md"
echo 2. 运行系统诊断工具
echo 3. 查看程序日志文件
echo.
echo 祝您使用愉快！
echo.
pause
exit /b 0
