using System;
using System.Text.Json;

namespace MemoryEnhancer.Utils
{
    /// <summary>
    /// 应用程序常量定义
    /// </summary>
    public static class Constants
    {
        #region 文件和路径常量

        /// <summary>
        /// 应用程序数据文件夹名称
        /// </summary>
        public const string AppDataFolderName = "MemoryEnhancer";

        /// <summary>
        /// 记忆项目文件名（用于兼容性）
        /// </summary>
        public const string MemoryItemsFileName = "memoryItems.json";

        /// <summary>
        /// 文件分组存储文件名
        /// </summary>
        public const string FileGroupsFileName = "fileGroups.json";

        /// <summary>
        /// 进度文件名
        /// </summary>
        public const string ProgressFileName = "progress.json";

        /// <summary>
        /// 统计数据文件名
        /// </summary>
        public const string StatisticsFileName = "statistics.json";

        /// <summary>
        /// 日志文件夹名称
        /// </summary>
        public const string LogsFolderName = "Logs";

        /// <summary>
        /// 临时文件扩展名
        /// </summary>
        public const string TempFileExtension = ".tmp";

        #endregion

        #region 时间和间隔常量

        /// <summary>
        /// 最小间隔时间（秒）
        /// </summary>
        public const int MinIntervalTimeSeconds = 0;

        /// <summary>
        /// 最大间隔时间（秒）
        /// </summary>
        public const int MaxIntervalTimeSeconds = 8;

        /// <summary>
        /// 默认间隔时间（秒）
        /// </summary>
        public const int DefaultIntervalTimeSeconds = 3;

        /// <summary>
        /// 延迟保存时间（秒）
        /// </summary>
        public const int DelaySaveTimeSeconds = 2;

        /// <summary>
        /// 日志保留天数
        /// </summary>
        public const int LogRetentionDays = 7;

        #endregion

        #region 学习和记忆常量

        /// <summary>
        /// 默认每日新项目学习限制
        /// </summary>
        public const int DefaultDailyNewItemsLimit = 20;

        /// <summary>
        /// 默认每日复习项目限制
        /// </summary>
        public const int DefaultDailyReviewItemsLimit = 50;

        /// <summary>
        /// 最小每日学习项目数量（确保有内容可学习）
        /// </summary>
        public const int MinDailyItemsCount = 10;

        /// <summary>
        /// SuperMemo算法默认难度系数
        /// </summary>
        public const double DefaultEaseFactor = 2.5;

        /// <summary>
        /// SuperMemo算法最小难度系数
        /// </summary>
        public const double MinEaseFactor = 1.3;

        #endregion

        #region UI和显示常量

        /// <summary>
        /// 默认字体大小
        /// </summary>
        public const double DefaultFontSize = 16.0;

        /// <summary>
        /// 最小字体大小
        /// </summary>
        public const double MinFontSize = 8.0;

        /// <summary>
        /// 最大字体大小
        /// </summary>
        public const double MaxFontSize = 72.0;

        /// <summary>
        /// 默认字体家族
        /// </summary>
        public const string DefaultFontFamily = "Microsoft YaHei";

        #endregion

        #region 文件格式常量

        /// <summary>
        /// 支持的文本文件扩展名
        /// </summary>
        public static readonly string[] SupportedTextExtensions = { ".txt" };

        /// <summary>
        /// 支持的CSV文件扩展名
        /// </summary>
        public static readonly string[] SupportedCsvExtensions = { ".csv" };

        /// <summary>
        /// 支持的JSON文件扩展名
        /// </summary>
        public static readonly string[] SupportedJsonExtensions = { ".json" };

        /// <summary>
        /// 支持的Excel文件扩展名
        /// </summary>
        public static readonly string[] SupportedExcelExtensions = { ".xlsx", ".xls" };

        /// <summary>
        /// 支持的Word文件扩展名
        /// </summary>
        public static readonly string[] SupportedWordExtensions = { ".docx", ".doc" };

        /// <summary>
        /// 所有支持的文件扩展名
        /// </summary>
        public static readonly string[] AllSupportedExtensions =
        {
            ".txt", ".csv", ".json", ".xlsx", ".xls", ".docx", ".doc"
        };

        #endregion

        #region 错误消息常量

        /// <summary>
        /// 文件不存在错误消息
        /// </summary>
        public const string FileNotFoundMessage = "文件不存在";

        /// <summary>
        /// 权限不足错误消息
        /// </summary>
        public const string UnauthorizedAccessMessage = "无法访问文件：权限不足";

        /// <summary>
        /// 文件格式错误消息
        /// </summary>
        public const string FileFormatErrorMessage = "文件格式错误";

        /// <summary>
        /// IO错误消息
        /// </summary>
        public const string IOErrorMessage = "文件读写时发生IO错误";

        /// <summary>
        /// 未知错误消息
        /// </summary>
        public const string UnknownErrorMessage = "发生未知错误";

        #endregion

        #region JSON序列化选项

        /// <summary>
        /// 获取标准的JSON序列化选项
        /// </summary>
        /// <returns>JSON序列化选项</returns>
        public static JsonSerializerOptions GetJsonSerializerOptions()
        {
            return new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNameCaseInsensitive = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
        }

        #endregion

        #region 应用程序信息

        /// <summary>
        /// 应用程序名称
        /// </summary>
        public const string ApplicationName = "记忆增强器";

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public const string ApplicationVersion = "2.0.0";

        /// <summary>
        /// 应用程序作者
        /// </summary>
        public const string ApplicationAuthor = "MemoryEnhancer Team";

        #endregion
    }
}
