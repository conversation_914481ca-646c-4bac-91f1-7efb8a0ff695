using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using MemoryEnhancer.Exceptions;

namespace MemoryEnhancer.Utils
{
    /// <summary>
    /// 文件导入辅助类
    /// </summary>
    public static class FileImportHelper
    {
        /// <summary>
        /// 根据文件扩展名确定导入方法并执行导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        public static bool ImportFile(string filePath, List<MemoryItem> items)
        {
            if (!File.Exists(filePath))
            {
                throw new ImportException(Constants.FileNotFoundMessage, filePath);
            }

            var extension = Path.GetExtension(filePath).ToLower();

            switch (extension)
            {
                case ".txt":
                    return ImportFromTxt(filePath, items);
                case ".csv":
                    return ImportFromCsv(filePath, items);
                case ".json":
                    return ImportFromJson(filePath, items);
                case ".xlsx":
                case ".xls":
                    throw new ImportException("Excel文件导入功能已移除，请将内容复制到文本文件中导入", filePath);
                case ".docx":
                case ".doc":
                    throw new ImportException("Word文档导入功能已移除，请将内容复制到文本文件中导入", filePath);
                default:
                    return ImportFromTxt(filePath, items); // 默认按文本文件处理
            }
        }

        /// <summary>
        /// 检查文件扩展名是否受支持
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否受支持</returns>
        public static bool IsSupportedFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return Constants.AllSupportedExtensions.Contains(extension);
        }

        /// <summary>
        /// 从文本文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromTxt(string filePath, List<MemoryItem> items)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                var lines = content.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

                int count = 0;
                foreach (var line in lines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        items.Add(new MemoryItem(line.Trim()));
                        count++;
                    }
                }

                Logger.Info($"从文本文件导入了 {count} 个项目");
                return count > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"从文本文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从文本文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        /// <summary>
        /// 从CSV文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromCsv(string filePath, List<MemoryItem> items)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                var lines = content.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

                int count = 0;
                foreach (var line in lines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        // 分割CSV行，取第一列作为内容
                        var columns = line.Split(',');
                        if (columns.Length > 0 && !string.IsNullOrWhiteSpace(columns[0]))
                        {
                            // 移除可能的引号
                            var cellContent = columns[0].Trim().Trim('"');
                            items.Add(new MemoryItem(cellContent));
                            count++;
                        }
                    }
                }

                Logger.Info($"从CSV文件导入了 {count} 个项目");
                return count > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"从CSV文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从CSV文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        /// <summary>
        /// 从JSON文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromJson(string filePath, List<MemoryItem> items)
        {
            try
            {
                var json = File.ReadAllText(filePath);

                // 尝试解析为字符串数组
                try
                {
                    var contentArray = JsonSerializer.Deserialize<string[]>(json, Constants.GetJsonSerializerOptions());
                    if (contentArray != null && contentArray.Length > 0)
                    {
                        int count = 0;
                        foreach (var content in contentArray)
                        {
                            if (!string.IsNullOrWhiteSpace(content))
                            {
                                items.Add(new MemoryItem(content.Trim()));
                                count++;
                            }
                        }
                        Logger.Info($"从JSON文件导入了 {count} 个项目（字符串数组格式）");
                        return count > 0;
                    }
                }
                catch
                {
                    // 如果字符串数组解析失败，尝试对象数组格式
                }

                // 尝试解析为对象数组，每个对象有content属性
                var objectArray = JsonSerializer.Deserialize<JsonElement>(json, Constants.GetJsonSerializerOptions());

                if (objectArray.ValueKind == JsonValueKind.Array)
                {
                    int count = 0;
                    foreach (var item in objectArray.EnumerateArray())
                    {
                        if (item.TryGetProperty("content", out JsonElement contentElement) ||
                            item.TryGetProperty("Content", out contentElement))
                        {
                            string? content = contentElement.GetString();
                            if (!string.IsNullOrWhiteSpace(content))
                            {
                                items.Add(new MemoryItem(content.Trim()));
                                count++;
                            }
                        }
                    }
                    Logger.Info($"从JSON文件导入了 {count} 个项目（对象数组格式）");
                    return count > 0;
                }

                throw new ImportException("JSON格式不正确，请确保是字符串数组或包含content字段的对象数组", filePath);
            }
            catch (ImportException)
            {
                throw; // 重新抛出ImportException
            }
            catch (Exception ex)
            {
                Logger.Error($"从JSON文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从JSON文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        // Excel和Word导入功能已移除，以减少外部依赖
        // 用户可以将Excel和Word内容复制到文本文件中进行导入
    }
}
