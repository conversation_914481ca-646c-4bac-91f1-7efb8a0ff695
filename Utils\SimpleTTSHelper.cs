#nullable disable
using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace MemoryEnhancer.Utils
{
    /// <summary>
    /// 简化的TTS辅助类，使用Windows内置SAPI，无需外部依赖
    /// </summary>
    public class SimpleTTSHelper : IDisposable
    {
        private dynamic speechVoice;
        private bool isInitialized = false;
        private bool isSpeaking = false;
        private bool disposed = false;

        public event EventHandler SpeakCompleted;

        /// <summary>
        /// 初始化TTS
        /// </summary>
        public bool Initialize()
        {
            try
            {
                Logger.Info("开始初始化简化TTS功能");

                // 尝试创建SAPI语音对象
                Type speechVoiceType = Type.GetTypeFromProgID("SAPI.SpVoice");
                if (speechVoiceType == null)
                {
                    Logger.Warning("无法获取SAPI.SpVoice类型");
                    return false;
                }

                speechVoice = Activator.CreateInstance(speechVoiceType);
                if (speechVoice == null)
                {
                    Logger.Warning("无法创建SAPI语音对象");
                    return false;
                }

                // 设置语音参数
                speechVoice.Rate = 0;    // 正常语速
                speechVoice.Volume = 100; // 最大音量

                isInitialized = true;
                Logger.Info("简化TTS功能初始化成功");
                return true;
            }
            catch (COMException ex)
            {
                Logger.Error("TTS初始化失败 - COM错误", ex);
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error("TTS初始化失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查TTS是否可用
        /// </summary>
        public bool IsAvailable => isInitialized && speechVoice != null;

        /// <summary>
        /// 同步朗读文本
        /// </summary>
        public bool Speak(string text)
        {
            if (!IsAvailable || string.IsNullOrWhiteSpace(text) || speechVoice == null)
                return false;

            try
            {
                isSpeaking = true;
                speechVoice.Speak(text, 0); // 0 = 同步模式
                isSpeaking = false;
                SpeakCompleted?.Invoke(this, EventArgs.Empty);
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("朗读失败", ex);
                isSpeaking = false;
                return false;
            }
        }

        /// <summary>
        /// 异步朗读文本
        /// </summary>
        public bool SpeakAsync(string text)
        {
            if (!IsAvailable || string.IsNullOrWhiteSpace(text) || speechVoice == null)
                return false;

            try
            {
                isSpeaking = true;

                // 在后台线程中执行朗读
                Task.Run(() =>
                {
                    try
                    {
                        speechVoice?.Speak(text, 1); // 1 = 异步模式
                        isSpeaking = false;
                        SpeakCompleted?.Invoke(this, EventArgs.Empty);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("异步朗读失败", ex);
                        isSpeaking = false;
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("启动异步朗读失败", ex);
                isSpeaking = false;
                return false;
            }
        }

        /// <summary>
        /// 停止朗读
        /// </summary>
        public void Stop()
        {
            if (!IsAvailable || speechVoice == null)
                return;

            try
            {
                speechVoice.Speak("", 2); // 2 = 停止当前朗读
                isSpeaking = false;
            }
            catch (Exception ex)
            {
                Logger.Warning($"停止朗读时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试TTS功能
        /// </summary>
        public bool TestTTS()
        {
            if (!IsAvailable || speechVoice == null)
                return false;

            try
            {
                // 静音测试
                int originalVolume = speechVoice.Volume;
                speechVoice.Volume = 0;
                speechVoice.Speak("test", 0);
                speechVoice.Volume = originalVolume;
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error("TTS测试失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取可用语音数量
        /// </summary>
        public int GetVoiceCount()
        {
            if (!IsAvailable || speechVoice == null)
                return 0;

            try
            {
                var voices = speechVoice.GetVoices();
                return voices.Count;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 是否正在朗读
        /// </summary>
        public bool IsSpeaking => isSpeaking;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    try
                    {
                        Stop();
                        if (speechVoice != null)
                        {
                            Marshal.ReleaseComObject(speechVoice);
                            speechVoice = null;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning($"释放TTS资源时出错: {ex.Message}");
                    }
                }
                disposed = true;
            }
        }

        ~SimpleTTSHelper()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// TTS诊断结果（简化版）
    /// </summary>
    public class SimpleTTSDiagnosticResult
    {
        public bool CanCreateSynthesizer { get; set; }
        public bool CanGetVoices { get; set; }
        public bool CanSynthesize { get; set; }
        public bool ServicesRunning { get; set; }
        public int VoiceCount { get; set; }
        public SimpleTTSStatus OverallStatus { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;

        public string GetStatusDescription()
        {
            return OverallStatus switch
            {
                SimpleTTSStatus.FullyFunctional => "TTS功能完全可用",
                SimpleTTSStatus.PartiallyFunctional => "TTS功能部分可用",
                SimpleTTSStatus.Limited => "TTS功能受限",
                SimpleTTSStatus.NotAvailable => "TTS功能不可用",
                SimpleTTSStatus.Error => $"TTS功能检测出错: {ErrorMessage}",
                _ => "未知状态"
            };
        }
    }

    /// <summary>
    /// 简化的TTS诊断辅助类
    /// </summary>
    public static class SimpleTTSDiagnostic
    {
        public static SimpleTTSDiagnosticResult RunDiagnostic()
        {
            var result = new SimpleTTSDiagnosticResult();

            try
            {
                Logger.Info("开始简化TTS诊断测试");

                // 测试1: 基本TTS创建
                using (var tts = new SimpleTTSHelper())
                {
                    result.CanCreateSynthesizer = tts.Initialize();

                    if (result.CanCreateSynthesizer)
                    {
                        // 测试2: 语音数量获取
                        result.VoiceCount = tts.GetVoiceCount();
                        result.CanGetVoices = result.VoiceCount > 0;

                        // 测试3: 语音合成测试
                        result.CanSynthesize = tts.TestTTS();
                    }
                }

                // 测试4: 服务状态（简化检查）
                result.ServicesRunning = CheckBasicServices();

                // 综合评估
                result.OverallStatus = EvaluateOverallStatus(result);

                Logger.Info($"简化TTS诊断完成，总体状态: {result.OverallStatus}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error("简化TTS诊断测试失败", ex);
                result.OverallStatus = SimpleTTSStatus.Error;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        private static bool CheckBasicServices()
        {
            try
            {
                // 简化的服务检查，只检查基本的COM组件是否可用
                Type speechVoiceType = Type.GetTypeFromProgID("SAPI.SpVoice");
                return speechVoiceType != null;
            }
            catch
            {
                return false;
            }
        }

        private static SimpleTTSStatus EvaluateOverallStatus(SimpleTTSDiagnosticResult result)
        {
            if (result.CanCreateSynthesizer && result.CanGetVoices &&
                result.CanSynthesize && result.VoiceCount > 0)
            {
                return SimpleTTSStatus.FullyFunctional;
            }
            else if (result.CanCreateSynthesizer && result.CanGetVoices && result.VoiceCount > 0)
            {
                return SimpleTTSStatus.PartiallyFunctional;
            }
            else if (result.CanCreateSynthesizer)
            {
                return SimpleTTSStatus.Limited;
            }
            else
            {
                return SimpleTTSStatus.NotAvailable;
            }
        }
    }

    /// <summary>
    /// 简化TTS状态枚举
    /// </summary>
    public enum SimpleTTSStatus
    {
        FullyFunctional,      // 完全可用
        PartiallyFunctional,  // 部分可用
        Limited,              // 功能受限
        NotAvailable,         // 不可用
        Error                 // 错误
    }
}
