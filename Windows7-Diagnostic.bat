@echo off
echo ========================================
echo Windows 7 Memory Enhancer Diagnostic Tool
echo ========================================
echo Version: 1.0.1 Enhanced Diagnostic
echo.

echo Starting comprehensive Windows 7 system diagnosis...
echo.

:: Check OS version
echo [1] Operating System Information:
echo ----------------------------------------
ver
echo.
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Detected version: %VERSION%
if "%VERSION%"=="6.1" (
    echo [OK] Windows 7 confirmed
) else (
    echo [WARNING] Not Windows 7 - this tool is optimized for Windows 7
)
echo.

:: Check system architecture
echo [2] System Architecture:
echo ----------------------------------------
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo [OK] 64-bit system detected
    echo Processor: %PROCESSOR_IDENTIFIER%
) else (
    echo [INFO] 32-bit system detected
    echo Processor: %PROCESSOR_IDENTIFIER%
)
echo Number of processors: %NUMBER_OF_PROCESSORS%
echo.

:: Check .NET Framework version
echo [3] .NET Framework Version Check:
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" /v Release >nul 2>&1
if %errorlevel% equ 0 (
    echo .NET Framework 4.x is installed
) else (
    echo Warning: .NET Framework 4.x not found
)
echo.

:: Check Windows Audio service
echo [4] Windows Audio Service Status:
sc query AudioSrv | find "STATE"
echo.

:: Check speech synthesis related services
echo [5] Speech Related Service Check:
echo ----------------------------------------
echo Checking AudioEndpointBuilder:
sc query AudioEndpointBuilder | find "STATE"
if %errorlevel% equ 0 (
    echo [OK] AudioEndpointBuilder service found
) else (
    echo [ERROR] AudioEndpointBuilder service not found or not accessible
)

echo.
echo Checking additional audio services:
sc query Audiosrv | find "STATE" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Audiosrv service found
) else (
    echo [INFO] Audiosrv service not found (this is normal on some systems)
)

echo.
echo Checking SAPI components:
if exist "%SystemRoot%\System32\Speech\Common\sapi.dll" (
    echo [OK] SAPI ^(64-bit^) found
) else (
    echo [ERROR] SAPI ^(64-bit^) not found
)

if exist "%SystemRoot%\SysWOW64\Speech\Common\sapi.dll" (
    echo [OK] SAPI ^(32-bit^) found
) else (
    echo [ERROR] SAPI ^(32-bit^) not found
)

echo.
echo Checking .NET Framework Speech components:
if exist "%SystemRoot%\Microsoft.NET\Framework\v4.0.30319\System.Speech.dll" (
    echo [OK] System.Speech.dll ^(.NET 4.0^) found
) else (
    echo [WARNING] System.Speech.dll ^(.NET 4.0^) not found
)

if exist "%SystemRoot%\Microsoft.NET\Framework64\v4.0.30319\System.Speech.dll" (
    echo [OK] System.Speech.dll ^(.NET 4.0 x64^) found
) else (
    echo [WARNING] System.Speech.dll ^(.NET 4.0 x64^) not found
)

echo.
echo Checking Windows Speech API registry:
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Speech API registry entries found
) else (
    echo [WARNING] Speech API registry entries not found
)
echo.

:: Check voice pack installation
echo [6] Voice Pack Check:
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices" >nul 2>&1
if %errorlevel% equ 0 (
    echo Found speech registry entries
    reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" >nul 2>&1
    if %errorlevel% equ 0 (
        echo Voice packs are installed
    ) else (
        echo Warning: No installed voice packs found
    )
) else (
    echo Warning: No speech-related registry entries found
)
echo.

:: Check audio devices
echo [7] Audio Device Check:
wmic sounddev get name >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: Unable to get audio device information
)
echo.

:: Check program files
echo [8] Program File Check:
if exist "MemoryEnhancer.exe" (
    echo [OK] Main program file exists
    dir "MemoryEnhancer.exe" | find "MemoryEnhancer.exe"
) else (
    echo [ERROR] Main program file not found
)

if exist "System.Speech.dll" (
    echo [OK] Speech library file exists
) else (
    echo [ERROR] Speech library file not found
)
echo.

:: Check dependency files
echo [9] Dependency File Check:
set "missing_files="
if not exist "Microsoft.Extensions.Configuration.dll" set "missing_files=%missing_files% Microsoft.Extensions.Configuration.dll"
if not exist "EPPlus.dll" set "missing_files=%missing_files% EPPlus.dll"
if not exist "DocumentFormat.OpenXml.dll" set "missing_files=%missing_files% DocumentFormat.OpenXml.dll"

if "%missing_files%"=="" (
    echo [OK] All dependency files exist
) else (
    echo [ERROR] Missing dependency files:%missing_files%
)
echo.

:: Check memory and disk space
echo [10] System Resource Check:
echo Available Memory:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | find "="
echo.
echo Disk Space:
dir | find "bytes free"
echo.

echo ========================================
echo Diagnostic Complete
echo ========================================
echo.
echo If problems are found, please refer to the following solutions:
echo.
echo 1. Speech Function Issues:
echo    - Install Microsoft Speech Platform
echo    - Download and install Chinese voice packs
echo    - Start Windows Audio service
echo.
echo 2. Program Startup Issues:
echo    - Run as administrator
echo    - Check antivirus software settings
echo    - Re-download program files
echo.
echo 3. Performance Issues:
echo    - Close unnecessary programs
echo    - Clean up disk space
echo    - Restart computer
echo.

pause
