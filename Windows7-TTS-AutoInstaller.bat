@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Windows 7 TTS 自动安装器
echo ========================================
echo 版本: 1.0 - 自动下载安装版
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [信息] 管理员权限确认
echo.

:: 创建下载目录
set "DOWNLOAD_DIR=%~dp0TTS_Components"
if not exist "%DOWNLOAD_DIR%" mkdir "%DOWNLOAD_DIR%"

echo [1] 准备下载 TTS 组件...
echo ----------------------------------------
echo 下载目录: %DOWNLOAD_DIR%
echo.

:: 定义下载链接和文件名
set "RUNTIME_URL=https://download.microsoft.com/download/3/4/9/349B0943-F0D6-4B1F-9259-4207B4B8F78A/SpeechPlatformRuntime.msi"
set "RUNTIME_FILE=%DOWNLOAD_DIR%\SpeechPlatformRuntime.msi"

set "VOICE_HUIHUI_URL=https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_HuiHui.msi"
set "VOICE_HUIHUI_FILE=%DOWNLOAD_DIR%\MSSpeech_TTS_zh-CN_HuiHui.msi"

set "VOICE_KANGKANG_URL=https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_Kangkang.msi"
set "VOICE_KANGKANG_FILE=%DOWNLOAD_DIR%\MSSpeech_TTS_zh-CN_Kangkang.msi"

:: 检查是否已存在文件
echo [2] 检查现有文件...
echo ----------------------------------------

if exist "%RUNTIME_FILE%" (
    echo [OK] Speech Platform Runtime 已存在
    set "RUNTIME_EXISTS=1"
) else (
    echo [下载] 需要下载 Speech Platform Runtime
    set "RUNTIME_EXISTS=0"
)

if exist "%VOICE_HUIHUI_FILE%" (
    echo [OK] 中文语音包（慧慧）已存在
    set "HUIHUI_EXISTS=1"
) else (
    echo [下载] 需要下载中文语音包（慧慧）
    set "HUIHUI_EXISTS=0"
)

if exist "%VOICE_KANGKANG_FILE%" (
    echo [OK] 中文语音包（康康）已存在
    set "KANGKANG_EXISTS=1"
) else (
    echo [下载] 需要下载中文语音包（康康）
    set "KANGKANG_EXISTS=0"
)

echo.

:: 下载文件
echo [3] 开始下载组件...
echo ----------------------------------------

if "%RUNTIME_EXISTS%"=="0" (
    echo 正在下载 Speech Platform Runtime...
    echo 文件大小: 约 5MB
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%RUNTIME_URL%' -OutFile '%RUNTIME_FILE%' -UseBasicParsing}" 2>nul
    if exist "%RUNTIME_FILE%" (
        echo [成功] Speech Platform Runtime 下载完成
    ) else (
        echo [失败] Speech Platform Runtime 下载失败
        goto manual_download
    )
)

if "%HUIHUI_EXISTS%"=="0" (
    echo 正在下载中文语音包（慧慧）...
    echo 文件大小: 约 60MB
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%VOICE_HUIHUI_URL%' -OutFile '%VOICE_HUIHUI_FILE%' -UseBasicParsing}" 2>nul
    if exist "%VOICE_HUIHUI_FILE%" (
        echo [成功] 中文语音包（慧慧）下载完成
    ) else (
        echo [失败] 中文语音包（慧慧）下载失败
    )
)

echo.

:: 安装组件
echo [4] 开始安装组件...
echo ----------------------------------------

echo 安装顺序很重要，请按提示操作：
echo.

if exist "%RUNTIME_FILE%" (
    echo 步骤 1: 安装 Speech Platform Runtime
    echo 文件: %RUNTIME_FILE%
    echo.
    set /p install_runtime="是否现在安装 Speech Platform Runtime? (Y/N): "
    if /i "!install_runtime!"=="Y" (
        echo 正在启动安装程序...
        start /wait msiexec /i "%RUNTIME_FILE%" /quiet /norestart
        if !errorlevel! equ 0 (
            echo [成功] Speech Platform Runtime 安装完成
        ) else (
            echo [警告] 安装可能遇到问题，错误代码: !errorlevel!
        )
    )
    echo.
)

if exist "%VOICE_HUIHUI_FILE%" (
    echo 步骤 2: 安装中文语音包（慧慧 - 女声）
    echo 文件: %VOICE_HUIHUI_FILE%
    echo.
    set /p install_huihui="是否现在安装中文语音包（慧慧）? (Y/N): "
    if /i "!install_huihui!"=="Y" (
        echo 正在启动安装程序...
        start /wait msiexec /i "%VOICE_HUIHUI_FILE%" /quiet /norestart
        if !errorlevel! equ 0 (
            echo [成功] 中文语音包（慧慧）安装完成
        ) else (
            echo [警告] 安装可能遇到问题，错误代码: !errorlevel!
        )
    )
    echo.
)

:: 安装完成提示
echo [5] 安装完成
echo ----------------------------------------
echo.
echo 重要提醒:
echo 1. 必须重启计算机才能使 TTS 功能生效
echo 2. 重启后重新运行"我要记忆"程序
echo 3. 如果仍有问题，请运行 Windows7-Diagnostic.bat 诊断
echo.

set /p restart_now="是否现在重启计算机? (Y/N): "
if /i "%restart_now%"=="Y" (
    echo 正在重启计算机...
    shutdown /r /t 10 /c "安装 TTS 组件后重启系统"
    echo 系统将在 10 秒后重启...
) else (
    echo 请记得稍后手动重启计算机以使 TTS 功能生效
)

goto end

:manual_download
echo.
echo ========================================
echo 手动下载说明
echo ========================================
echo.
echo 自动下载失败，请手动下载以下文件到 TTS_Components 文件夹：
echo.
echo 1. Speech Platform Runtime 11.0 (x64)
echo    下载地址: https://www.microsoft.com/en-us/download/details.aspx?id=27225
echo    文件名: SpeechPlatformRuntime.msi
echo.
echo 2. 中文语音包
echo    下载地址: https://www.microsoft.com/en-us/download/details.aspx?id=27224
echo    文件名: MSSpeech_TTS_zh-CN_HuiHui.msi
echo.
echo 下载完成后，请重新运行此脚本进行安装。
echo.

:end
echo.
echo 安装器执行完成。
pause
