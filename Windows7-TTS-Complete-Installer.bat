@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Windows 7 TTS 完整安装器 v2.0
echo ========================================
echo 专为Windows 7系统设计的TTS语音功能安装工具
echo 包含自动下载、安装和验证功能
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要管理员权限
    echo.
    echo 请按以下步骤操作：
    echo 1. 右键点击此文件
    echo 2. 选择"以管理员身份运行"
    echo 3. 在UAC提示中点击"是"
    echo.
    pause
    exit /b 1
)

echo [✓] 管理员权限确认
echo.

:: 检查操作系统版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo [信息] 检测到系统版本: %VERSION%
if not "%VERSION%"=="6.1" (
    echo [警告] 此工具专为 Windows 7 (6.1) 设计
    echo 当前系统可能不需要此安装器
    echo.
    set /p continue="是否继续安装? (Y/N): "
    if /i not "!continue!"=="Y" exit /b 0
)
echo.

:: 创建下载目录
set "DOWNLOAD_DIR=%~dp0TTS_Components"
if not exist "%DOWNLOAD_DIR%" mkdir "%DOWNLOAD_DIR%"

echo [1] 系统诊断检查
echo ========================================

:: 检查现有TTS组件
echo 正在检查现有TTS组件...

:: 检查SAPI组件
if exist "%SystemRoot%\System32\Speech\Common\sapi.dll" (
    echo [✓] SAPI (64位) 已安装
    set "SAPI_64_OK=1"
) else (
    echo [!] SAPI (64位) 未安装
    set "SAPI_64_OK=0"
)

if exist "%SystemRoot%\SysWOW64\Speech\Common\sapi.dll" (
    echo [✓] SAPI (32位) 已安装
    set "SAPI_32_OK=1"
) else (
    echo [!] SAPI (32位) 未安装
    set "SAPI_32_OK=0"
)

:: 检查语音包注册表
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] 发现语音包注册表项
    for /f %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^| find /c "HKEY"') do (
        echo [信息] 已安装语音包数量: %%i
        set "VOICE_COUNT=%%i"
    )
) else (
    echo [!] 未发现语音包注册表项
    set "VOICE_COUNT=0"
)

:: 检查Windows Audio服务
sc query AudioSrv | find "RUNNING" >nul
if %errorlevel% equ 0 (
    echo [✓] Windows Audio 服务正在运行
    set "AUDIO_SERVICE_OK=1"
) else (
    echo [!] Windows Audio 服务未运行
    echo 正在尝试启动服务...
    sc start AudioSrv >nul 2>&1
    if %errorlevel% equ 0 (
        echo [✓] Windows Audio 服务启动成功
        set "AUDIO_SERVICE_OK=1"
    ) else (
        echo [✗] Windows Audio 服务启动失败
        set "AUDIO_SERVICE_OK=0"
    )
)

echo.
echo [2] 安装建议分析
echo ========================================

set "NEED_RUNTIME=0"
set "NEED_VOICES=0"

if "%VOICE_COUNT%"=="0" (
    echo [建议] 需要安装语音包
    set "NEED_VOICES=1"
)

if "%SAPI_64_OK%"=="0" (
    echo [建议] 需要安装 Speech Platform Runtime
    set "NEED_RUNTIME=1"
)

if "%NEED_RUNTIME%"=="0" and "%NEED_VOICES%"=="0" (
    echo [信息] 系统已安装TTS组件
    echo 如果程序仍然无法使用语音功能，可能需要：
    echo 1. 重启计算机
    echo 2. 检查程序权限
    echo 3. 重新安装语音包
    echo.
    set /p force_install="是否强制重新安装? (Y/N): "
    if /i not "!force_install!"=="Y" goto test_tts
    set "NEED_RUNTIME=1"
    set "NEED_VOICES=1"
)

echo.
echo [3] 开始下载和安装
echo ========================================

:: 定义下载链接
set "RUNTIME_URL=https://download.microsoft.com/download/3/4/9/349B0943-F0D6-4B1F-9259-4207B4B8F78A/SpeechPlatformRuntime.msi"
set "VOICE_HUIHUI_URL=https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_HuiHui.msi"

set "RUNTIME_FILE=%DOWNLOAD_DIR%\SpeechPlatformRuntime.msi"
set "VOICE_FILE=%DOWNLOAD_DIR%\MSSpeech_TTS_zh-CN_HuiHui.msi"

:: 下载和安装 Speech Platform Runtime
if "%NEED_RUNTIME%"=="1" (
    echo.
    echo 步骤 1: Speech Platform Runtime
    echo ----------------------------------------
    
    if not exist "%RUNTIME_FILE%" (
        echo 正在下载 Speech Platform Runtime (约5MB)...
        powershell -Command "& {try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Invoke-WebRequest -Uri '%RUNTIME_URL%' -OutFile '%RUNTIME_FILE%' -UseBasicParsing -TimeoutSec 300 } catch { Write-Host 'Download failed:' $_.Exception.Message; exit 1 }}"
        
        if !errorlevel! neq 0 (
            echo [✗] 下载失败，请检查网络连接
            goto manual_download
        )
    )
    
    if exist "%RUNTIME_FILE%" (
        echo [✓] 文件已准备就绪
        echo 正在安装 Speech Platform Runtime...
        start /wait msiexec /i "%RUNTIME_FILE%" /quiet /norestart
        
        if !errorlevel! equ 0 (
            echo [✓] Speech Platform Runtime 安装成功
        ) else (
            echo [✗] Speech Platform Runtime 安装失败 (错误代码: !errorlevel!)
            echo 可能原因：权限不足、文件损坏或系统不兼容
        )
    ) else (
        echo [✗] 无法找到安装文件
        goto manual_download
    )
)

:: 下载和安装语音包
if "%NEED_VOICES%"=="1" (
    echo.
    echo 步骤 2: 中文语音包
    echo ----------------------------------------
    
    if not exist "%VOICE_FILE%" (
        echo 正在下载中文语音包 - 慧慧女声 (约60MB)...
        echo 请耐心等待，文件较大...
        powershell -Command "& {try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Invoke-WebRequest -Uri '%VOICE_HUIHUI_URL%' -OutFile '%VOICE_FILE%' -UseBasicParsing -TimeoutSec 600 } catch { Write-Host 'Download failed:' $_.Exception.Message; exit 1 }}"
        
        if !errorlevel! neq 0 (
            echo [✗] 下载失败，请检查网络连接
            goto manual_download
        )
    )
    
    if exist "%VOICE_FILE%" (
        echo [✓] 文件已准备就绪
        echo 正在安装中文语音包...
        start /wait msiexec /i "%VOICE_FILE%" /quiet /norestart
        
        if !errorlevel! equ 0 (
            echo [✓] 中文语音包安装成功
        ) else (
            echo [✗] 中文语音包安装失败 (错误代码: !errorlevel!)
            echo 可能原因：权限不足、磁盘空间不足或依赖组件缺失
        )
    ) else (
        echo [✗] 无法找到安装文件
        goto manual_download
    )
)

goto test_tts

:manual_download
echo.
echo ========================================
echo 手动下载指导
echo ========================================
echo.
echo 自动下载失败，请手动下载以下文件：
echo.
echo 1. Microsoft Speech Platform Runtime 11.0 (x64)
echo    下载地址: https://www.microsoft.com/en-us/download/details.aspx?id=27225
echo    保存为: %RUNTIME_FILE%
echo.
echo 2. 中文语音包 - 慧慧
echo    下载地址: https://www.microsoft.com/en-us/download/details.aspx?id=27224
echo    保存为: %VOICE_FILE%
echo.
echo 下载完成后，请重新运行此脚本。
echo.
pause
exit /b 1

:test_tts
echo.
echo [4] 安装验证测试
echo ========================================

echo 正在测试TTS功能...

:: 创建测试脚本
echo Set objVoice = CreateObject("SAPI.SpVoice") > "%TEMP%\tts_test.vbs"
echo objVoice.Volume = 50 >> "%TEMP%\tts_test.vbs"
echo objVoice.Speak "TTS功能测试成功" >> "%TEMP%\tts_test.vbs"

:: 运行测试
cscript //nologo "%TEMP%\tts_test.vbs" >nul 2>&1
set "TEST_RESULT=%errorlevel%"

:: 清理测试文件
del "%TEMP%\tts_test.vbs" >nul 2>&1

if "%TEST_RESULT%"=="0" (
    echo [✓] TTS功能测试成功！
    echo 如果您听到了语音，说明安装完成。
) else (
    echo [!] TTS功能测试失败
    echo 可能需要重启计算机后才能正常工作。
)

echo.
echo [5] 安装完成
echo ========================================
echo.
echo 重要提醒：
echo 1. 必须重启计算机才能确保TTS功能完全生效
echo 2. 重启后重新运行"我要记忆"程序
echo 3. 如果仍有问题，请运行 Windows7-Diagnostic.bat 诊断
echo.

set /p restart_now="是否现在重启计算机? (Y/N): "
if /i "%restart_now%"=="Y" (
    echo 系统将在10秒后重启...
    shutdown /r /t 10 /c "TTS组件安装完成，重启系统使更改生效"
) else (
    echo 请记得稍后手动重启计算机以使TTS功能完全生效。
)

echo.
echo 安装器执行完成。
pause
