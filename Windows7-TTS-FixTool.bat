@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Windows 7 TTS 问题修复工具 v1.0
echo ========================================
echo 专门解决Windows 7系统TTS语音功能问题
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要管理员权限
    echo.
    echo 请按以下步骤操作：
    echo 1. 右键点击此文件
    echo 2. 选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo [✓] 管理员权限确认
echo.

:: 检查操作系统版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
if not "%VERSION%"=="6.1" (
    echo [警告] 此工具专为 Windows 7 设计
    echo 当前系统版本: %VERSION%
    echo.
    set /p continue="是否继续? (Y/N): "
    if /i not "!continue!"=="Y" exit /b 0
)

echo [1] 系统诊断
echo ========================================

:: 检查Windows Audio服务
echo 检查Windows Audio服务...
sc query AudioSrv | find "RUNNING" >nul
if %errorlevel% equ 0 (
    echo [✓] Windows Audio服务正在运行
    set "AUDIO_OK=1"
) else (
    echo [!] Windows Audio服务未运行
    echo 正在尝试启动服务...
    sc start AudioSrv >nul 2>&1
    timeout /t 3 >nul
    sc query AudioSrv | find "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo [✓] Windows Audio服务启动成功
        set "AUDIO_OK=1"
    ) else (
        echo [✗] Windows Audio服务启动失败
        set "AUDIO_OK=0"
    )
)

:: 检查SAPI组件
echo 检查SAPI组件...
if exist "%SystemRoot%\System32\Speech\Common\sapi.dll" (
    echo [✓] SAPI (64位) 已安装
    set "SAPI_64_OK=1"
) else (
    echo [!] SAPI (64位) 未安装
    set "SAPI_64_OK=0"
)

if exist "%SystemRoot%\SysWOW64\Speech\Common\sapi.dll" (
    echo [✓] SAPI (32位) 已安装
    set "SAPI_32_OK=1"
) else (
    echo [!] SAPI (32位) 未安装
    set "SAPI_32_OK=0"
)

:: 检查语音包
echo 检查语音包...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" >nul 2>&1
if %errorlevel% equ 0 (
    for /f %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^| find /c "HKEY"') do (
        set "VOICE_COUNT=%%i"
    )
    if !VOICE_COUNT! gtr 0 (
        echo [✓] 检测到 !VOICE_COUNT! 个语音包
        set "VOICES_OK=1"
    ) else (
        echo [!] 未检测到语音包
        set "VOICES_OK=0"
    )
) else (
    echo [!] 语音包注册表项不存在
    set "VOICES_OK=0"
    set "VOICE_COUNT=0"
)

:: 检查Speech Platform Runtime
echo 检查Speech Platform Runtime...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech_OneCore\Preferences" >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Speech Platform Runtime 已安装
    set "RUNTIME_OK=1"
) else (
    reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Preferences" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [✓] 传统Speech API 已安装
        set "RUNTIME_OK=1"
    ) else (
        echo [!] Speech Platform Runtime 未安装
        set "RUNTIME_OK=0"
    )
)

echo.
echo [2] 问题分析
echo ========================================

set "ISSUES_FOUND=0"
set "CRITICAL_ISSUES=0"

if "%AUDIO_OK%"=="0" (
    echo [严重] Windows Audio服务无法启动
    set /a CRITICAL_ISSUES+=1
    set /a ISSUES_FOUND+=1
)

if "%RUNTIME_OK%"=="0" (
    echo [严重] Speech Platform Runtime 未安装
    set /a CRITICAL_ISSUES+=1
    set /a ISSUES_FOUND+=1
)

if "%VOICES_OK%"=="0" (
    echo [重要] 语音包未安装或未正确注册
    set /a ISSUES_FOUND+=1
)

if "%SAPI_64_OK%"=="0" and "%SAPI_32_OK%"=="0" (
    echo [重要] SAPI组件缺失
    set /a ISSUES_FOUND+=1
)

if %ISSUES_FOUND% equ 0 (
    echo [✓] 未发现明显问题
    echo TTS组件看起来已正确安装
    echo.
    echo 如果程序仍然无法使用语音功能，可能的原因：
    echo 1. 程序权限不足
    echo 2. 防病毒软件阻止
    echo 3. 音频设备问题
    echo 4. 需要重启计算机
    goto test_tts
) else (
    echo.
    echo 发现 %ISSUES_FOUND% 个问题，其中 %CRITICAL_ISSUES% 个严重问题
)

echo.
echo [3] 自动修复
echo ========================================

:: 修复Windows Audio服务
if "%AUDIO_OK%"=="0" (
    echo 正在修复Windows Audio服务...
    
    :: 停止相关服务
    sc stop AudioSrv >nul 2>&1
    sc stop AudioEndpointBuilder >nul 2>&1
    timeout /t 2 >nul
    
    :: 重新启动服务
    sc start AudioEndpointBuilder >nul 2>&1
    timeout /t 2 >nul
    sc start AudioSrv >nul 2>&1
    timeout /t 3 >nul
    
    :: 检查修复结果
    sc query AudioSrv | find "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo [✓] Windows Audio服务修复成功
    ) else (
        echo [✗] Windows Audio服务修复失败
        echo 建议：重启计算机后再试
    )
)

:: 修复语音包注册
if "%VOICES_OK%"=="0" and "%RUNTIME_OK%"=="1" (
    echo 正在尝试重新注册语音包...
    
    :: 重新注册SAPI组件
    regsvr32 /s "%SystemRoot%\System32\Speech\Common\sapi.dll" 2>nul
    regsvr32 /s "%SystemRoot%\SysWOW64\Speech\Common\sapi.dll" 2>nul
    
    :: 刷新语音包注册表
    reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices" /f >nul 2>&1
    
    echo [✓] 语音包重新注册完成
)

:: 如果缺少关键组件，提供安装选项
if "%RUNTIME_OK%"=="0" or "%VOICES_OK%"=="0" (
    echo.
    echo 检测到缺少关键TTS组件，需要安装：
    echo.
    if "%RUNTIME_OK%"=="0" (
        echo - Microsoft Speech Platform Runtime 11.0
    )
    if "%VOICES_OK%"=="0" (
        echo - 中文语音包
    )
    echo.
    
    set /p install_components="是否现在安装缺少的组件? (Y/N): "
    if /i "!install_components!"=="Y" (
        if exist "Windows7-TTS-Complete-Installer.bat" (
            echo 正在启动完整安装器...
            call "Windows7-TTS-Complete-Installer.bat"
        ) else if exist "Windows7-TTS-AutoInstaller.bat" (
            echo 正在启动自动安装器...
            call "Windows7-TTS-AutoInstaller.bat"
        ) else (
            echo [!] 找不到TTS安装器
            echo 请手动下载并安装以下组件：
            echo.
            echo 1. Microsoft Speech Platform Runtime 11.0 (x64)
            echo    https://www.microsoft.com/en-us/download/details.aspx?id=27225
            echo.
            echo 2. 中文语音包
            echo    https://www.microsoft.com/en-us/download/details.aspx?id=27224
        )
    )
)

:test_tts
echo.
echo [4] 功能测试
echo ========================================

echo 正在测试TTS功能...

:: 创建测试脚本
echo Set objVoice = CreateObject("SAPI.SpVoice") > "%TEMP%\tts_test.vbs"
echo On Error Resume Next >> "%TEMP%\tts_test.vbs"
echo objVoice.Volume = 30 >> "%TEMP%\tts_test.vbs"
echo objVoice.Speak "TTS功能测试" >> "%TEMP%\tts_test.vbs"
echo If Err.Number ^<^> 0 Then >> "%TEMP%\tts_test.vbs"
echo     WScript.Quit 1 >> "%TEMP%\tts_test.vbs"
echo End If >> "%TEMP%\tts_test.vbs"

:: 运行测试
cscript //nologo "%TEMP%\tts_test.vbs" >nul 2>&1
set "TEST_RESULT=%errorlevel%"

:: 清理测试文件
del "%TEMP%\tts_test.vbs" >nul 2>&1

if "%TEST_RESULT%"=="0" (
    echo [✓] TTS功能测试成功！
    echo 如果您听到了语音，说明TTS功能正常工作。
) else (
    echo [✗] TTS功能测试失败
    echo.
    echo 可能的原因：
    echo 1. 语音包未正确安装
    echo 2. 音频设备问题
    echo 3. 需要重启计算机
    echo 4. 系统权限限制
)

echo.
echo [5] 修复完成
echo ========================================

if "%TEST_RESULT%"=="0" (
    echo [成功] TTS功能修复完成！
    echo "我要记忆"程序现在应该可以正常使用语音功能了。
) else (
    echo [部分成功] 已完成可能的修复操作
    echo.
    echo 如果问题仍然存在，建议：
    echo 1. 重启计算机
    echo 2. 检查音频设备和驱动程序
    echo 3. 运行 Windows7-Diagnostic.bat 获取详细诊断
    echo 4. 手动安装TTS组件
)

echo.
echo 修复工具执行完成。
pause
