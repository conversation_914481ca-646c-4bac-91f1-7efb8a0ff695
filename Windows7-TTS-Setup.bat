@echo off
echo ========================================
echo Windows 7 TTS 安装助手
echo ========================================
echo 版本: 1.0
echo.

echo 此工具将帮助您在 Windows 7 上安装语音合成功能
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要管理员权限
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo [信息] 管理员权限确认
echo.

:: 检查操作系统版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
if not "%VERSION%"=="6.1" (
    echo [警告] 此工具专为 Windows 7 设计
    echo 当前系统版本: %VERSION%
    echo.
)

echo [1] 检查当前 TTS 状态...
echo ----------------------------------------

:: 检查 SAPI 组件
if exist "%SystemRoot%\System32\Speech\Common\sapi.dll" (
    echo [OK] SAPI (64位) 已安装
) else (
    echo [缺失] SAPI (64位) 未安装
)

if exist "%SystemRoot%\SysWOW64\Speech\Common\sapi.dll" (
    echo [OK] SAPI (32位) 已安装
) else (
    echo [缺失] SAPI (32位) 未安装
)

:: 检查语音包
echo.
echo [2] 检查语音包安装状态...
echo ----------------------------------------
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] 发现语音包注册表项
    for /f %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^| find /c "HKEY"') do (
        echo 已安装语音包数量: %%i
    )
) else (
    echo [缺失] 未发现语音包
)

echo.
echo [3] 检查 Windows Audio 服务...
echo ----------------------------------------
sc query AudioSrv | find "RUNNING" >nul
if %errorlevel% equ 0 (
    echo [OK] Windows Audio 服务正在运行
) else (
    echo [警告] Windows Audio 服务未运行
    echo 尝试启动服务...
    sc start AudioSrv >nul 2>&1
    if %errorlevel% equ 0 (
        echo [OK] 服务启动成功
    ) else (
        echo [错误] 服务启动失败
    )
)

echo.
echo ========================================
echo 安装建议
echo ========================================
echo.

echo 根据检查结果，建议您按以下步骤安装 TTS 组件：
echo.

echo 步骤 1: 下载 Microsoft Speech Platform Runtime 11.0
echo ------------------------------------------------
echo 下载地址: https://www.microsoft.com/en-us/download/details.aspx?id=27225
echo 文件名: SpeechPlatformRuntime.msi
echo 说明: 这是语音平台的核心运行时
echo.

echo 步骤 2: 下载中文语音包
echo ------------------------------------------------
echo 下载地址: https://www.microsoft.com/en-us/download/details.aspx?id=27224
echo 推荐语音包:
echo   - MSSpeech_TTS_zh-CN_HuiHui.msi (中文女声)
echo   - MSSpeech_TTS_zh-CN_Kangkang.msi (中文男声)
echo.

echo 步骤 3: 安装顺序
echo ------------------------------------------------
echo 1. 先安装 SpeechPlatformRuntime.msi
echo 2. 再安装语音包 (.msi 文件)
echo 3. 重启计算机
echo 4. 重新运行"我要记忆"程序
echo.

echo 步骤 4: 验证安装
echo ------------------------------------------------
echo 安装完成后，可以通过以下方式验证:
echo 1. 控制面板 → 语音识别 → 文本到语音转换
echo 2. 选择已安装的语音并点击"预览"
echo 3. 重新运行此诊断脚本
echo.

echo ========================================
echo 快速测试
echo ========================================
echo.

echo 是否要测试当前系统的 TTS 功能？
set /p choice="输入 Y 继续测试，或按任意键退出: "
if /i "%choice%"=="Y" goto test_tts
goto end

:test_tts
echo.
echo 正在测试 TTS 功能...

:: 创建临时 VBS 脚本进行测试
echo Set objVoice = CreateObject("SAPI.SpVoice") > temp_tts_test.vbs
echo objVoice.Speak "测试语音合成功能" >> temp_tts_test.vbs

:: 运行测试
cscript //nologo temp_tts_test.vbs >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] TTS 功能测试通过！
    echo 如果您听到了语音，说明 TTS 功能正常工作
) else (
    echo [失败] TTS 功能测试失败
    echo 请按照上述步骤安装必要组件
)

:: 清理临时文件
del temp_tts_test.vbs >nul 2>&1

:end
echo.
echo ========================================
echo 完成
echo ========================================
echo.
echo 如果您需要更多帮助，请查看 Windows7-用户手册.md
echo 或运行 Windows7-Diagnostic.bat 进行详细诊断
echo.

pause
