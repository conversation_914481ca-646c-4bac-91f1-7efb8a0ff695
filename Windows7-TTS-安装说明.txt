========================================
Windows 7 语音合成功能安装说明
========================================

如果您在 Windows 7 上看到"TTS 功能不可用"的提示，这是正常现象。
Windows 7 系统默认不包含完整的语音合成功能，需要额外安装。

========================================
快速解决方案
========================================

方法一：使用安装助手（推荐）
1. 双击运行 "Windows7-TTS-Setup.bat"
2. 按照提示下载并安装所需组件
3. 重启计算机

方法二：手动安装
1. 下载 Microsoft Speech Platform Runtime 11.0
   - 访问：https://www.microsoft.com/en-us/download/details.aspx?id=27225
   - 下载：SpeechPlatformRuntime.msi (x64版本)
   - 安装：双击运行安装程序

2. 下载中文语音包
   - 访问：https://www.microsoft.com/en-us/download/details.aspx?id=27224
   - 下载以下任一语音包：
     * MSSpeech_TTS_zh-CN_HuiHui.msi (中文女声)
     * MSSpeech_TTS_zh-CN_Kangkang.msi (中文男声)
   - 安装：双击运行安装程序

3. 重启计算机
   - 安装完成后必须重启系统

4. 验证安装
   - 控制面板 → 语音识别 → 文本到语音转换
   - 选择已安装的语音
   - 点击"预览"按钮测试

========================================
安装顺序很重要
========================================

必须按以下顺序安装：
1. 先安装 Speech Platform Runtime
2. 再安装语音包
3. 最后重启计算机

如果顺序错误，可能导致安装失败。

========================================
验证安装是否成功
========================================

方法一：通过控制面板
1. 打开控制面板
2. 点击"语音识别"
3. 选择"文本到语音转换"选项卡
4. 在"语音选择"下拉菜单中应该能看到已安装的语音
5. 点击"预览"按钮，应该能听到语音

方法二：重新运行程序
1. 重启计算机后
2. 重新运行"我要记忆"程序
3. 如果安装成功，程序启动时不会再显示TTS错误

方法三：运行诊断工具
1. 双击运行 "Windows7-Diagnostic.bat"
2. 查看语音包检查结果
3. 如果显示"已安装语音包数量: 1"或更多，说明安装成功

========================================
常见问题
========================================

问题1：下载链接无法访问
解决：使用搜索引擎搜索"Microsoft Speech Platform Runtime 11.0"和"中文语音包"

问题2：安装后仍然没有语音
解决：
- 确认安装顺序正确
- 确认已重启计算机
- 检查Windows Audio服务是否启动
- 尝试以管理员身份运行程序

问题3：只有英文语音，没有中文语音
解决：
- 确认下载了中文语音包
- 确认语音包安装成功
- 在控制面板中检查语音设置

问题4：安装文件下载失败
解决：
- 检查网络连接
- 尝试使用其他浏览器
- 联系系统管理员

========================================
技术支持
========================================

如果按照上述步骤仍无法解决问题：

1. 运行诊断工具
   - 双击 "Windows7-Diagnostic.bat"
   - 保存输出结果

2. 查看详细手册
   - 打开 "Windows7-用户手册.md"
   - 查看更详细的故障排除步骤

3. 检查日志文件
   - 位置：%APPDATA%\MemoryEnhancer\Logs\
   - 查看最新的日志文件

========================================
重要提醒
========================================

1. Windows 7 系统本身的限制
   - 这不是程序的问题，而是Windows 7系统的限制
   - 所有在Windows 7上使用TTS功能的程序都需要额外安装这些组件

2. 程序仍可正常使用
   - 即使没有语音功能，程序的其他功能完全正常
   - 可以正常浏览、学习、设置等

3. 一次安装，永久使用
   - 安装完成后，所有使用TTS功能的程序都能受益
   - 不需要为每个程序单独安装

========================================
