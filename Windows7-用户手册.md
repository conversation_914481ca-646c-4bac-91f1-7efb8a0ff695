# 我要记忆 - Windows 7 用户手册

## 系统要求

### 最低要求
- Windows 7 SP1 (64位)
- 2GB 内存
- 500MB 可用磁盘空间
- 音频设备（用于语音功能）

### 推荐配置
- Windows 7 SP1 或更高版本
- 4GB 或更多内存
- 1GB 可用磁盘空间
- 高质量音频设备

## 安装和启动

### 首次使用
1. 解压所有文件到一个文件夹
2. 双击 `Start-Windows7.bat` 启动程序
3. 如果遇到问题，右键选择"以管理员身份运行"

### 文件说明
- `MemoryEnhancer.exe` - 主程序
- `Start-Windows7.bat` - Windows 7专用启动器
- `Windows7-Diagnostic.bat` - 诊断工具
- `System.Speech.dll` - 语音功能库
- 其他 `.dll` 文件 - 程序依赖库

## 常见问题解决

### 1. 语音合成功能初始化失败

**症状**: 程序启动时弹出"语音合成功能初始化失败"

**解决方案**:
1. **检查Windows Audio服务**
   - 按 Win+R，输入 `services.msc`
   - 找到 "Windows Audio" 服务
   - 确保状态为"已启动"，启动类型为"自动"

2. **安装语音包**
   - 打开控制面板 → 语音识别
   - 点击"高级语音选项"
   - 安装中文语音包

3. **检查音频设备**
   - 确保音频设备正常工作
   - 检查音量设置
   - 更新音频驱动程序

4. **以管理员身份运行**
   - 右键点击 `Start-Windows7.bat`
   - 选择"以管理员身份运行"

### 2. 设置窗口不见了

**症状**: 点击设置按钮后窗口消失

**解决方案**:
1. **检查任务栏**
   - 设置窗口可能最小化到任务栏
   - 点击任务栏中的程序图标

2. **重启程序**
   - 关闭程序
   - 使用 `Start-Windows7.bat` 重新启动

3. **检查屏幕分辨率**
   - 设置窗口可能超出屏幕范围
   - 调整屏幕分辨率或缩放设置

4. **清除设置文件**
   - 删除 `%APPDATA%\MemoryEnhancer` 文件夹
   - 重新启动程序

### 3. 程序启动失败

**症状**: 双击程序无反应或立即关闭

**解决方案**:
1. **运行诊断工具**
   ```
   双击 Windows7-Diagnostic.bat
   ```

2. **检查依赖文件**
   - 确保所有 .dll 文件都在程序目录中
   - 重新下载完整的程序包

3. **检查系统更新**
   - 安装最新的Windows 7更新
   - 安装 .NET Framework 4.7.2 或更高版本

4. **防病毒软件**
   - 将程序目录添加到防病毒软件白名单
   - 临时禁用实时保护测试

### 4. 语音功能不工作（Windows 7 专用解决方案）

**症状**: 程序正常运行但没有语音，显示"TTS 功能不可用"

**Windows 7 需要额外安装 TTS 组件**:

#### 方法一：自动安装（推荐）
1. **下载 Microsoft Speech Platform Runtime 11.0**
   - 访问 Microsoft 官网下载页面
   - 下载 `SpeechPlatformRuntime.msi` (x64 版本)
   - 双击安装

2. **下载中文语音包**
   - 下载 `MSSpeech_TTS_zh-CN_HuiHui.msi` (中文女声)
   - 或 `MSSpeech_TTS_zh-CN_Kangkang.msi` (中文男声)
   - 双击安装

3. **重启计算机**
   - 安装完成后重启系统
   - 重新运行程序

#### 方法二：通过控制面板
1. **打开控制面板**
   - 控制面板 → 轻松访问 → 语音识别
   - 点击"高级语音选项"

2. **配置文本到语音转换**
   - 在"文本到语音转换"选项卡中
   - 选择可用的语音
   - 点击"预览"测试

3. **如果没有可用语音**
   - 需要先安装 Speech Platform Runtime
   - 然后安装语音包

#### 方法三：手动检查和修复
1. **检查注册表**
   ```
   运行 regedit
   导航到: HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens
   ```

2. **检查系统文件**
   ```
   C:\Windows\System32\Speech\Common\sapi.dll
   C:\Windows\SysWOW64\Speech\Common\sapi.dll
   ```

3. **运行系统文件检查**
   ```
   以管理员身份运行命令提示符
   执行: sfc /scannow
   ```

#### 下载链接（Microsoft 官方）
- **Speech Platform Runtime 11.0 (x64)**
  - 文件名: `SpeechPlatformRuntime.msi`
  - 大小: 约 5MB

- **中文语音包**
  - 慧慧（女声）: `MSSpeech_TTS_zh-CN_HuiHui.msi`
  - 康康（男声）: `MSSpeech_TTS_zh-CN_Kangkang.msi`
  - 大小: 约 50-100MB 每个

**注意**: 必须先安装 Runtime，再安装语音包，最后重启系统。

## 性能优化

### 内存优化
- 关闭不必要的后台程序
- 定期清理系统垃圾文件
- 确保至少有1GB可用内存

### 启动优化
- 使用 `Start-Windows7.bat` 启动
- 以管理员身份运行以获得最佳性能
- 将程序添加到防病毒软件白名单

### 存储优化
- 定期清理程序日志文件
- 将程序安装在SSD上（如果有）
- 确保有足够的磁盘空间

## 高级设置

### 日志文件位置
```
%APPDATA%\MemoryEnhancer\Logs\
```

### 配置文件位置
```
%APPDATA%\MemoryEnhancer\
```

### 手动清理
如果程序出现异常，可以删除配置文件夹重置设置：
```
删除文件夹: %APPDATA%\MemoryEnhancer
```

## 故障排除工具

### 诊断工具
运行 `Windows7-Diagnostic.bat` 可以：
- 检查系统环境
- 验证依赖文件
- 测试语音功能
- 检查系统服务

### 日志分析
查看日志文件了解详细错误信息：
```
%APPDATA%\MemoryEnhancer\Logs\MemoryEnhancer_YYYYMMDD.log
```

## 技术支持

### 收集诊断信息
遇到问题时，请收集以下信息：
1. 运行 `Windows7-Diagnostic.bat` 的输出
2. 程序日志文件
3. 错误截图
4. 系统版本信息

### 常用命令
```batch
# 检查Windows版本
ver

# 检查服务状态
sc query AudioSrv

# 检查语音包
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens"
```

## 更新和维护

### 程序更新
- 下载新版本前备份配置文件
- 解压到新文件夹
- 复制旧的配置文件到新文件夹

### 定期维护
- 每月清理一次日志文件
- 检查Windows更新
- 更新音频驱动程序

---

**注意**: 本程序专为Windows 7优化，在其他操作系统上可能需要不同的配置。
