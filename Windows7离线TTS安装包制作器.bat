@echo off
setlocal enabledelayedexpansion

title Windows 7 离线TTS安装包制作器

echo ========================================
echo Windows 7 离线TTS安装包制作器 v1.0
echo ========================================
echo 为无网络的Windows 7系统制作完整的TTS安装包
echo.

:: 创建离线安装包目录
set "OFFLINE_DIR=%~dp0Windows7-TTS-离线安装包"
if not exist "%OFFLINE_DIR%" (
    mkdir "%OFFLINE_DIR%"
    echo [✓] 创建离线安装包目录: %OFFLINE_DIR%
) else (
    echo [✓] 离线安装包目录已存在: %OFFLINE_DIR%
)
echo.

echo [1] 收集必需文件
echo ========================================

set "FILES_FOUND=0"
set "TOTAL_FILES=0"

:: 检查慧慧语音包
set /a TOTAL_FILES+=1
if exist "MSSpeech_TTS_zh-CN_HuiHui.msi" (
    copy "MSSpeech_TTS_zh-CN_HuiHui.msi" "%OFFLINE_DIR%\" >nul
    echo [✓] 慧慧语音包: MSSpeech_TTS_zh-CN_HuiHui.msi
    set /a FILES_FOUND+=1
) else if exist "huihui.msi" (
    copy "huihui.msi" "%OFFLINE_DIR%\MSSpeech_TTS_zh-CN_HuiHui.msi" >nul
    echo [✓] 慧慧语音包: huihui.msi (已重命名)
    set /a FILES_FOUND+=1
) else (
    echo [!] 未找到慧慧语音包文件
    echo     请将语音包文件放在当前目录并重命名为:
    echo     MSSpeech_TTS_zh-CN_HuiHui.msi 或 huihui.msi
)

:: 检查Speech Platform Runtime
set /a TOTAL_FILES+=1
if exist "SpeechPlatformRuntime.msi" (
    copy "SpeechPlatformRuntime.msi" "%OFFLINE_DIR%\" >nul
    echo [✓] Speech Platform Runtime: SpeechPlatformRuntime.msi
    set /a FILES_FOUND+=1
) else (
    echo [!] 未找到Speech Platform Runtime
    echo     建议下载: SpeechPlatformRuntime.msi
)

:: 检查康康语音包（可选）
if exist "MSSpeech_TTS_zh-CN_Kangkang.msi" (
    copy "MSSpeech_TTS_zh-CN_Kangkang.msi" "%OFFLINE_DIR%\" >nul
    echo [✓] 康康语音包: MSSpeech_TTS_zh-CN_Kangkang.msi (可选)
)

:: 检查eSpeak-NG组件（备用方案）
if exist "espeak-ng-X64.msi" (
    copy "espeak-ng-X64.msi" "%OFFLINE_DIR%\" >nul
    echo [✓] eSpeak-NG安装版: espeak-ng-X64.msi (备用方案)
)

if exist "espeak-ng-X64.zip" (
    copy "espeak-ng-X64.zip" "%OFFLINE_DIR%\" >nul
    echo [✓] eSpeak-NG便携版: espeak-ng-X64.zip (备用方案)
)

echo.

echo [2] 创建离线安装脚本
echo ========================================

:: 创建主安装脚本
(
echo @echo off
echo setlocal enabledelayedexpansion
echo.
echo title Windows 7 TTS 离线安装器
echo.
echo echo ========================================
echo echo Windows 7 TTS 离线安装器
echo echo ========================================
echo echo 专为无网络Windows 7系统设计
echo echo.
echo.
echo :: 检查管理员权限
echo net session ^>nul 2^>^&1
echo if %%errorlevel%% neq 0 ^(
echo     echo [错误] 需要管理员权限！
echo     echo.
echo     echo 请按以下步骤操作：
echo     echo 1. 右键点击此文件
echo     echo 2. 选择"以管理员身份运行"
echo     echo.
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo [✓] 管理员权限确认
echo echo.
echo.
echo :: 检查系统版本
echo for /f "tokens=4-5 delims=. " %%%%i in ^('ver'^) do set VERSION=%%%%i.%%%%j
echo if "%%VERSION%%"=="6.1" ^(
echo     echo [✓] Windows 7 系统确认
echo ^) else ^(
echo     echo [!] 当前系统: Windows %%VERSION%%
echo     echo [提示] 此安装包主要为Windows 7设计
echo     set /p continue="是否继续安装? ^(Y/N^): "
echo     if /i "^^!continue^^!"=="N" exit /b 0
echo ^)
echo echo.
echo.
echo echo [1] 系统诊断
echo echo ========================================
echo.
echo :: 检查Windows Audio服务
echo sc query AudioSrv ^| find "RUNNING" ^>nul
echo if %%errorlevel%% equ 0 ^(
echo     echo [✓] Windows Audio服务正在运行
echo ^) else ^(
echo     echo [!] Windows Audio服务未运行，正在启动...
echo     sc start AudioSrv ^>nul 2^>^&1
echo     timeout /t 3 ^>nul
echo     sc query AudioSrv ^| find "RUNNING" ^>nul
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] Windows Audio服务启动成功
echo     ^) else ^(
echo         echo [✗] Windows Audio服务启动失败
echo     ^)
echo ^)
echo.
echo :: 检查现有语音包
echo reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^>nul 2^>^&1
echo if %%errorlevel%% equ 0 ^(
echo     for /f %%%%i in ^('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^^^| find /c "HKEY"'^) do ^(
echo         echo [信息] 检测到 %%%%i 个现有语音包
echo     ^)
echo ^) else ^(
echo     echo [信息] 未检测到现有语音包
echo ^)
echo echo.
echo.
echo echo [2] 开始安装TTS组件
echo echo ========================================
echo.
echo set "INSTALL_SUCCESS=0"
echo.
echo :: 安装Speech Platform Runtime
echo if exist "SpeechPlatformRuntime.msi" ^(
echo     echo [1/2] 正在安装 Speech Platform Runtime...
echo     echo 这是语音平台的核心运行时组件
echo     start /wait msiexec /i "SpeechPlatformRuntime.msi" /quiet /norestart
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] Speech Platform Runtime 安装成功
echo         set /a INSTALL_SUCCESS+=1
echo     ^) else ^(
echo         echo [✗] Speech Platform Runtime 安装失败
echo     ^)
echo     echo.
echo ^) else ^(
echo     echo [跳过] 未找到 SpeechPlatformRuntime.msi
echo ^)
echo.
echo :: 安装慧慧语音包
echo if exist "MSSpeech_TTS_zh-CN_HuiHui.msi" ^(
echo     echo [2/2] 正在安装慧慧中文语音包...
echo     echo 这是高质量的中文女声语音包
echo     start /wait msiexec /i "MSSpeech_TTS_zh-CN_HuiHui.msi" /quiet /norestart
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] 慧慧语音包安装成功
echo         set /a INSTALL_SUCCESS+=1
echo     ^) else ^(
echo         echo [✗] 慧慧语音包安装失败
echo     ^)
echo     echo.
echo ^) else ^(
echo     echo [错误] 未找到 MSSpeech_TTS_zh-CN_HuiHui.msi
echo     echo 请确保语音包文件在当前目录中
echo ^)
echo.
echo :: 可选：安装康康语音包
echo if exist "MSSpeech_TTS_zh-CN_Kangkang.msi" ^(
echo     echo [可选] 正在安装康康中文语音包...
echo     start /wait msiexec /i "MSSpeech_TTS_zh-CN_Kangkang.msi" /quiet /norestart
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] 康康语音包安装成功
echo     ^) else ^(
echo         echo [✗] 康康语音包安装失败
echo     ^)
echo     echo.
echo ^)
echo.
echo echo [3] 安装验证
echo echo ========================================
echo.
echo if %%INSTALL_SUCCESS%% geq 1 ^(
echo     echo 正在验证安装结果...
echo     timeout /t 3 ^>nul
echo.
echo     :: 测试TTS功能
echo     echo Set objVoice = CreateObject^("SAPI.SpVoice"^) ^> "%%TEMP%%\tts_test.vbs"
echo     echo objVoice.Volume = 30 ^>^> "%%TEMP%%\tts_test.vbs"
echo     echo objVoice.Speak "TTS语音功能测试成功" ^>^> "%%TEMP%%\tts_test.vbs"
echo.
echo     cscript //nologo "%%TEMP%%\tts_test.vbs" ^>nul 2^>^&1
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] TTS功能测试成功！
echo         echo 如果您听到了语音，说明安装完成。
echo     ^) else ^(
echo         echo [!] TTS功能测试失败，可能需要重启计算机
echo     ^)
echo.
echo     del "%%TEMP%%\tts_test.vbs" ^>nul 2^>^&1
echo ^) else ^(
echo     echo [!] 没有成功安装任何组件
echo ^)
echo.
echo echo [4] 安装完成
echo echo ========================================
echo.
echo if %%INSTALL_SUCCESS%% geq 1 ^(
echo     echo [🎉] TTS组件安装完成！
echo     echo.
echo     echo 重要提醒：
echo     echo 1. 建议重启计算机以确保所有更改生效
echo     echo 2. 重启后运行"我要记忆"程序测试语音功能
echo     echo 3. 如果仍有问题，请检查音频设备设置
echo ^) else ^(
echo     echo [❌] TTS组件安装失败
echo     echo.
echo     echo 可能的解决方案：
echo     echo 1. 确保以管理员身份运行
echo     echo 2. 检查文件完整性
echo     echo 3. 尝试手动安装各个组件
echo     echo 4. 考虑使用备用方案^(eSpeak-NG^)
echo ^)
echo.
echo set /p restart_now="是否现在重启计算机? ^(Y/N^): "
echo if /i "%%restart_now%%"=="Y" ^(
echo     echo 系统将在10秒后重启...
echo     shutdown /r /t 10 /c "TTS组件安装完成，重启系统"
echo ^) else ^(
echo     echo 请记得稍后手动重启计算机
echo ^)
echo.
echo pause
) > "%OFFLINE_DIR%\安装TTS组件.bat"

echo [✓] 主安装脚本创建完成: 安装TTS组件.bat

:: 创建备用安装脚本（eSpeak-NG）
(
echo @echo off
echo echo ========================================
echo echo eSpeak-NG 备用TTS安装器
echo echo ========================================
echo echo 开源TTS解决方案，无需依赖Windows组件
echo echo.
echo.
echo if exist "espeak-ng-X64.msi" ^(
echo     echo 正在安装 eSpeak-NG ^(安装版^)...
echo     start /wait msiexec /i "espeak-ng-X64.msi" /quiet /norestart
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] eSpeak-NG 安装成功
echo         echo 测试语音功能...
echo         if exist "C:\Program Files\eSpeak NG\espeak-ng.exe" ^(
echo             "C:\Program Files\eSpeak NG\espeak-ng.exe" -v zh -s 150 "eSpeak-NG安装成功"
echo         ^)
echo     ^) else ^(
echo         echo [✗] eSpeak-NG 安装失败
echo     ^)
echo ^) else if exist "espeak-ng-X64.zip" ^(
echo     echo 正在解压 eSpeak-NG ^(便携版^)...
echo     powershell -Command "Expand-Archive -Path 'espeak-ng-X64.zip' -DestinationPath 'espeak-ng-portable' -Force"
echo     if exist "espeak-ng-portable" ^(
echo         echo [✓] eSpeak-NG 解压成功
echo         echo 测试语音功能...
echo         if exist "espeak-ng-portable\espeak-ng.exe" ^(
echo             "espeak-ng-portable\espeak-ng.exe" -v zh -s 150 "eSpeak-NG设置成功"
echo         ^)
echo     ^) else ^(
echo         echo [✗] eSpeak-NG 解压失败
echo     ^)
echo ^) else ^(
echo     echo [错误] 未找到eSpeak-NG安装文件
echo ^)
echo.
echo pause
) > "%OFFLINE_DIR%\安装eSpeak-NG备用方案.bat"

echo [✓] 备用安装脚本创建完成: 安装eSpeak-NG备用方案.bat

:: 创建手动安装指导
(
echo Windows 7 离线TTS安装包使用说明
echo =====================================
echo.
echo 📁 文件清单：
if exist "%OFFLINE_DIR%\MSSpeech_TTS_zh-CN_HuiHui.msi" echo ✓ MSSpeech_TTS_zh-CN_HuiHui.msi - 慧慧语音包^(主要^)
if exist "%OFFLINE_DIR%\SpeechPlatformRuntime.msi" echo ✓ SpeechPlatformRuntime.msi - 语音平台运行时
if exist "%OFFLINE_DIR%\MSSpeech_TTS_zh-CN_Kangkang.msi" echo ✓ MSSpeech_TTS_zh-CN_Kangkang.msi - 康康语音包^(可选^)
if exist "%OFFLINE_DIR%\espeak-ng-X64.msi" echo ✓ espeak-ng-X64.msi - eSpeak-NG安装版^(备用^)
if exist "%OFFLINE_DIR%\espeak-ng-X64.zip" echo ✓ espeak-ng-X64.zip - eSpeak-NG便携版^(备用^)
echo ✓ 安装TTS组件.bat - 主安装脚本
echo ✓ 安装eSpeak-NG备用方案.bat - 备用安装脚本
echo ✓ 使用说明.txt - 本文件
echo.
echo 🚀 快速安装^(推荐^)：
echo 1. 将整个文件夹复制到目标Windows 7电脑
echo 2. 右键点击"安装TTS组件.bat"
echo 3. 选择"以管理员身份运行"
echo 4. 按照提示完成安装
echo 5. 重启计算机
echo.
echo 📋 手动安装顺序：
echo 1. 先安装 SpeechPlatformRuntime.msi
echo 2. 再安装 MSSpeech_TTS_zh-CN_HuiHui.msi
echo 3. 可选安装 MSSpeech_TTS_zh-CN_Kangkang.msi
echo 4. 重启计算机
echo.
echo 🔧 备用方案^(如果主方案失败^)：
echo 1. 运行"安装eSpeak-NG备用方案.bat"
echo 2. 或手动安装eSpeak-NG组件
echo.
echo ⚠️ 重要提醒：
echo • 必须以管理员身份运行安装程序
echo • 安装完成后必须重启计算机
echo • 确保Windows Audio服务正在运行
echo • 如果遇到问题，尝试备用方案
echo.
echo 🔍 故障排除：
echo 1. 安装失败 → 检查管理员权限和文件完整性
echo 2. 无声音 → 检查音频设备和Windows Audio服务
echo 3. 程序不识别 → 重启计算机，重新运行程序
echo 4. 仍有问题 → 使用eSpeak-NG备用方案
echo.
echo 📞 技术支持：
echo • 检查Windows事件日志获取详细错误信息
echo • 确保系统满足最低要求^(Windows 7 SP1^)
echo • 考虑更新.NET Framework到4.0或更高版本
) > "%OFFLINE_DIR%\使用说明.txt"

echo [✓] 使用说明创建完成: 使用说明.txt

:: 创建诊断脚本
(
echo @echo off
echo echo ========================================
echo echo Windows 7 TTS 系统诊断工具
echo echo ========================================
echo echo.
echo.
echo echo [1] 系统信息
echo echo ----------------------------------------
echo echo 操作系统版本:
echo ver
echo echo.
echo echo 当前用户权限:
echo net session ^>nul 2^>^&1 ^&^& echo 管理员权限 ^|^| echo 普通用户权限
echo echo.
echo.
echo echo [2] 服务状态
echo echo ----------------------------------------
echo echo Windows Audio服务:
echo sc query AudioSrv
echo echo.
echo.
echo echo [3] TTS组件检查
echo echo ----------------------------------------
echo echo SAPI组件:
echo if exist "%%SystemRoot%%\System32\Speech\Common\sapi.dll" ^(echo [✓] SAPI 64位 已安装^) else ^(echo [!] SAPI 64位 未安装^)
echo if exist "%%SystemRoot%%\SysWOW64\Speech\Common\sapi.dll" ^(echo [✓] SAPI 32位 已安装^) else ^(echo [!] SAPI 32位 未安装^)
echo echo.
echo echo 语音包注册表:
echo reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" 2^>nul ^|^| echo 未找到语音包注册表项
echo echo.
echo.
echo echo [4] 音频设备
echo echo ----------------------------------------
echo echo 音频设备列表:
echo wmic sounddev get name 2^>nul ^|^| echo 无法获取音频设备信息
echo echo.
echo.
echo echo [5] TTS功能测试
echo echo ----------------------------------------
echo echo 正在测试TTS功能...
echo echo Set objVoice = CreateObject^("SAPI.SpVoice"^) ^> "%%TEMP%%\tts_test.vbs"
echo echo objVoice.Speak "TTS功能测试" ^>^> "%%TEMP%%\tts_test.vbs"
echo cscript //nologo "%%TEMP%%\tts_test.vbs" 2^>nul ^&^& echo [✓] TTS功能正常 ^|^| echo [!] TTS功能异常
echo del "%%TEMP%%\tts_test.vbs" ^>nul 2^>^&1
echo echo.
echo.
echo echo 诊断完成。
echo pause
) > "%OFFLINE_DIR%\系统诊断.bat"

echo [✓] 诊断脚本创建完成: 系统诊断.bat

echo.

echo [3] 离线安装包制作完成
echo ========================================

echo.
echo 📊 文件统计：
echo • 找到必需文件: %FILES_FOUND%/%TOTAL_FILES%
echo • 安装包位置: %OFFLINE_DIR%
echo.

echo 📋 包含内容：
dir "%OFFLINE_DIR%" /b

echo.
echo 🎯 使用方法：
echo 1. 将整个"%OFFLINE_DIR%"文件夹复制到目标Windows 7电脑
echo 2. 在目标电脑上运行"安装TTS组件.bat"（以管理员身份）
echo 3. 按照提示完成安装
echo 4. 重启计算机
echo.

if %FILES_FOUND% geq 1 (
    echo [✅] 离线安装包制作成功！
    echo 可以在无网络的Windows 7系统上使用。
) else (
    echo [⚠️] 离线安装包制作部分成功
    echo 请确保添加必需的语音包文件。
)

echo.
set /p open_folder="是否现在打开离线安装包文件夹? (Y/N): "
if /i "%open_folder%"=="Y" (
    explorer "%OFFLINE_DIR%"
)

echo.
echo 离线安装包制作器执行完成！
pause
