# Windows 7 离线TTS安装指南

## 🎯 适用场景

本指南专门为**无法上网的Windows 7系统**设计，帮助您在离线环境下安装TTS语音功能。

## 📦 准备离线安装包

### 在有网络的电脑上操作：

#### 第一步：收集文件
将以下文件放在同一个文件夹中：

**必需文件：**
- `MSSpeech_TTS_zh-CN_HuiHui.msi` - 慧慧语音包（您从博客园下载的）
- `SpeechPlatformRuntime.msi` - 语音平台运行时

**可选文件：**
- `MSSpeech_TTS_zh-CN_Kangkang.msi` - 康康语音包（男声）
- `espeak-ng-X64.msi` - eSpeak-NG安装版（备用方案）
- `espeak-ng-X64.zip` - eSpeak-NG便携版（备用方案）

#### 第二步：制作离线包
1. 运行 `Windows7离线TTS安装包制作器.bat`
2. 等待制作完成
3. 获得完整的离线安装包文件夹

#### 第三步：传输到目标电脑
- 使用U盘、移动硬盘或网络共享
- 将整个离线安装包文件夹复制到Windows 7电脑

## 🚀 在Windows 7电脑上安装

### 方法一：自动安装（推荐）

```
1. 进入离线安装包文件夹
2. 右键点击 "安装TTS组件.bat"
3. 选择 "以管理员身份运行"
4. 按照提示完成安装
5. 重启计算机
```

### 方法二：手动安装

如果自动安装失败，可以手动安装：

```
1. 右键点击 "SpeechPlatformRuntime.msi"
   → 选择 "以管理员身份运行"
   → 等待安装完成

2. 右键点击 "MSSpeech_TTS_zh-CN_HuiHui.msi"
   → 选择 "以管理员身份运行"
   → 等待安装完成

3. 重启计算机
```

### 方法三：备用方案（eSpeak-NG）

如果Microsoft TTS无法安装：

```
1. 运行 "安装eSpeak-NG备用方案.bat"
2. 或手动安装eSpeak-NG组件
3. 无需重启，直接可用
```

## 📋 离线安装包内容

制作完成的离线安装包包含：

```
Windows7-TTS-离线安装包/
├── MSSpeech_TTS_zh-CN_HuiHui.msi      # 慧慧语音包
├── SpeechPlatformRuntime.msi          # 语音平台运行时
├── 安装TTS组件.bat                     # 主安装脚本
├── 安装eSpeak-NG备用方案.bat           # 备用安装脚本
├── 系统诊断.bat                        # 诊断工具
├── 使用说明.txt                        # 详细说明
└── [其他可选组件...]
```

## ⚠️ 重要注意事项

### 安装前检查
- ✅ 确保是Windows 7 SP1或更高版本
- ✅ 确保有管理员权限
- ✅ 确保Windows Audio服务正在运行
- ✅ 关闭防病毒软件（临时）

### 安装过程中
- ⚠️ 必须以管理员身份运行所有安装程序
- ⚠️ 不要在安装过程中关闭窗口
- ⚠️ 耐心等待，安装可能需要几分钟

### 安装后
- 🔄 **必须重启计算机**
- 🔊 检查音频设备设置
- 🎵 测试"我要记忆"程序的语音功能

## 🔍 故障排除

### 常见问题

#### Q1: 安装失败，提示权限不足
**解决方案：**
```
1. 右键点击安装文件
2. 选择"以管理员身份运行"
3. 在UAC提示中点击"是"
```

#### Q2: 安装后没有声音
**解决方案：**
```
1. 重启计算机（必须）
2. 检查Windows Audio服务：
   - 开始菜单 → 运行 → services.msc
   - 找到"Windows Audio"服务
   - 确保状态为"已启动"
3. 检查音频设备和音量设置
```

#### Q3: 程序仍提示TTS不可用
**解决方案：**
```
1. 运行"系统诊断.bat"检查状态
2. 确认语音包已正确安装：
   - 控制面板 → 语音识别 → 文本到语音转换
   - 查看是否有"Microsoft Huihui"
3. 重新运行"我要记忆"程序
```

#### Q4: Microsoft TTS完全无法安装
**解决方案：**
```
使用eSpeak-NG备用方案：
1. 运行"安装eSpeak-NG备用方案.bat"
2. 或手动安装eSpeak-NG组件
3. 这是开源方案，兼容性更好
```

## 🛠️ 手动诊断命令

如果需要手动检查系统状态：

```batch
# 检查系统版本
ver

# 检查Windows Audio服务
sc query AudioSrv

# 检查语音包注册
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens"

# 测试TTS功能
echo Set objVoice = CreateObject("SAPI.SpVoice") > test.vbs
echo objVoice.Speak "测试" >> test.vbs
cscript test.vbs
del test.vbs
```

## 📊 验证安装成功

### 检查标志
- ✅ 控制面板中显示新的语音选项
- ✅ "我要记忆"程序可以正常朗读
- ✅ 听到清晰的中文语音
- ✅ 没有错误提示

### 测试步骤
1. 重启计算机
2. 打开"我要记忆"程序
3. 输入一些中文文本
4. 点击朗读按钮
5. 确认能听到慧慧语音

## 💡 优化建议

### 性能优化
- 定期清理系统临时文件
- 确保有足够的磁盘空间
- 更新音频驱动程序

### 使用技巧
- 调整语速和音量到合适水平
- 长文本建议分段朗读
- 定期检查Windows Audio服务状态

## 🎉 成功案例

安装成功后，您将获得：
- 🎵 高质量的中文女声（慧慧）
- 🔊 清晰自然的发音
- ⚡ 快速的响应速度
- 🔄 稳定的重复播放功能

---

**注意**: 此方案专为Windows 7离线环境设计。如果您的系统可以上网，建议使用在线安装工具获得更好的体验。
