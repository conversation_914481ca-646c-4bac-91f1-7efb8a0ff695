﻿#pragma checksum "..\..\..\..\..\Dialogs\SettingsDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3A6042C6F19E28FFAF77EAA7E64753E32CAD5D6B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MemoryEnhancer;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MemoryEnhancer {
    
    
    /// <summary>
    /// SettingsDialog
    /// </summary>
    public partial class SettingsDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider intervalTimeSlider;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox autoReadCheckBox;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox autoAdvanceCheckBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button textColorButton;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle textColorRectangle;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button bgColorButton;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle bgColorRectangle;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider newItemsSlider;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider reviewItemsSlider;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox fileGroupComboBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox todayOnlyCheckBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox fontFamilyComboBox;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider fontSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox boldCheckBox;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox italicCheckBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock fontPreviewText;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox algorithmComboBox;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock algorithmDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider repeatCountSlider;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView fileGroupsListView;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView statisticsListView;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MemoryEnhancer;component/dialogs/settingsdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 20 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowKeyboardShortcuts_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 21 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowAbout_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.intervalTimeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 4:
            this.autoReadCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.autoAdvanceCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.textColorButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            this.textColorButton.Click += new System.Windows.RoutedEventHandler(this.TextColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.textColorRectangle = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 8:
            this.bgColorButton = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            this.bgColorButton.Click += new System.Windows.RoutedEventHandler(this.BgColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.bgColorRectangle = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 10:
            this.newItemsSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 11:
            this.reviewItemsSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 12:
            this.fileGroupComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 134 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            this.fileGroupComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FileGroupComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.todayOnlyCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 14:
            
            #line 146 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ImportButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.fontFamilyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 16:
            this.fontSizeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 17:
            this.boldCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.italicCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.fontPreviewText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.algorithmComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 213 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            this.algorithmComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.AlgorithmComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.algorithmDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.repeatCountSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 23:
            this.fileGroupsListView = ((System.Windows.Controls.ListView)(target));
            
            #line 245 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            this.fileGroupsListView.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FileGroupsListView_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 256 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RenameFileGroup_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 257 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteFileGroup_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 258 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshFileGroups_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.statisticsListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 28:
            
            #line 277 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshStats_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 288 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 289 "..\..\..\..\..\Dialogs\SettingsDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

