{"version": 3, "targets": {".NETCoreApp,Version=v3.1": {}, ".NETCoreApp,Version=v3.1/win7-x64": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETCoreApp,Version=v3.1": []}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "K:\\xiecx\\VC\\我要记忆 1.0 0526 b win 7\\MemoryEnhancer.csproj", "projectName": "MemoryEnhancer", "projectPath": "K:\\xiecx\\VC\\我要记忆 1.0 0526 b win 7\\MemoryEnhancer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "K:\\xiecx\\VC\\我要记忆 1.0 0526 b win 7\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[3.1.10, 3.1.10]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[3.1.32, 3.1.32]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[3.1.32, 3.1.32]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[3.1.0, 3.1.0]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[3.1.32, 3.1.32]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[3.1.0, 3.1.0]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[3.1.32, 3.1.32]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win7-x64": {"#import": []}}}}