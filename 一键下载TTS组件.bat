@echo off
setlocal enabledelayedexpansion

title TTS组件一键下载工具

echo ========================================
echo TTS 组件一键下载工具
echo ========================================
echo 快速下载Windows 7所需的TTS组件
echo.

:: 创建下载目录
set "DOWNLOAD_DIR=%~dp0TTS组件"
if not exist "%DOWNLOAD_DIR%" mkdir "%DOWNLOAD_DIR%"

echo 📁 下载目录: %DOWNLOAD_DIR%
echo.

:: 检查网络连接
echo 🌐 检查网络连接...
ping -n 1 download.microsoft.com >nul 2>&1
if %errorlevel% neq 0 (
    echo [!] 无法连接到Microsoft下载服务器
    echo 请检查网络连接后重试
    pause
    exit /b 1
)
echo [✓] 网络连接正常
echo.

:: 定义下载文件
set "FILES_TO_DOWNLOAD=3"
set "DOWNLOADED_COUNT=0"

echo 📦 准备下载以下组件：
echo.
echo 1. Microsoft Speech Platform Runtime 11.0 (约5MB)
echo 2. 中文语音包 - 慧慧女声 (约60MB)  
echo 3. 中文语音包 - 康康男声 (约60MB)
echo.
echo 总计大小约: 125MB
echo.

set /p confirm="确认开始下载? (Y/N) [Y]: "
if /i "%confirm%"=="N" exit /b 0

echo.
echo 🚀 开始下载...
echo ========================================

:: 下载1: Speech Platform Runtime
echo.
echo [1/3] 下载 Speech Platform Runtime...
set "RUNTIME_URL=https://download.microsoft.com/download/3/4/9/349B0943-F0D6-4B1F-9259-4207B4B8F78A/SpeechPlatformRuntime.msi"
set "RUNTIME_FILE=%DOWNLOAD_DIR%\SpeechPlatformRuntime.msi"

if exist "%RUNTIME_FILE%" (
    echo [✓] 文件已存在，跳过下载
    set /a DOWNLOADED_COUNT+=1
) else (
    echo 正在下载... 请稍候
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri '%RUNTIME_URL%' -OutFile '%RUNTIME_FILE%' -UseBasicParsing; Write-Host '[✓] 下载成功' } catch { Write-Host '[✗] 下载失败:' $_.Exception.Message; exit 1 }}"
    
    if !errorlevel! equ 0 (
        set /a DOWNLOADED_COUNT+=1
    )
)

:: 下载2: 慧慧语音包
echo.
echo [2/3] 下载中文语音包（慧慧-女声）...
set "HUIHUI_URL=https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_HuiHui.msi"
set "HUIHUI_FILE=%DOWNLOAD_DIR%\MSSpeech_TTS_zh-CN_HuiHui.msi"

if exist "%HUIHUI_FILE%" (
    echo [✓] 文件已存在，跳过下载
    set /a DOWNLOADED_COUNT+=1
) else (
    echo 正在下载... 文件较大，请耐心等待
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri '%HUIHUI_URL%' -OutFile '%HUIHUI_FILE%' -UseBasicParsing; Write-Host '[✓] 下载成功' } catch { Write-Host '[✗] 下载失败:' $_.Exception.Message; exit 1 }}"
    
    if !errorlevel! equ 0 (
        set /a DOWNLOADED_COUNT+=1
    )
)

:: 下载3: 康康语音包
echo.
echo [3/3] 下载中文语音包（康康-男声）...
set "KANGKANG_URL=https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_Kangkang.msi"
set "KANGKANG_FILE=%DOWNLOAD_DIR%\MSSpeech_TTS_zh-CN_Kangkang.msi"

if exist "%KANGKANG_FILE%" (
    echo [✓] 文件已存在，跳过下载
    set /a DOWNLOADED_COUNT+=1
) else (
    echo 正在下载... 文件较大，请耐心等待
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; try { Invoke-WebRequest -Uri '%KANGKANG_URL%' -OutFile '%KANGKANG_FILE%' -UseBasicParsing; Write-Host '[✓] 下载成功' } catch { Write-Host '[✗] 下载失败:' $_.Exception.Message; exit 1 }}"
    
    if !errorlevel! equ 0 (
        set /a DOWNLOADED_COUNT+=1
    )
)

:: 创建快速安装脚本
echo.
echo 📝 创建安装脚本...

(
echo @echo off
echo echo ========================================
echo echo TTS 组件快速安装
echo echo ========================================
echo echo.
echo.
echo :: 检查管理员权限
echo net session ^>nul 2^>^&1
echo if %%errorlevel%% neq 0 ^(
echo     echo [错误] 需要管理员权限！
echo     echo 请右键点击此文件，选择"以管理员身份运行"
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo [✓] 管理员权限确认
echo echo.
echo.
echo echo 正在安装TTS组件...
echo echo.
echo.
echo :: 安装Runtime
echo if exist "SpeechPlatformRuntime.msi" ^(
echo     echo [1/2] 安装 Speech Platform Runtime...
echo     msiexec /i "SpeechPlatformRuntime.msi" /quiet /norestart
echo     echo [✓] Runtime安装完成
echo ^)
echo.
echo :: 安装语音包
echo if exist "MSSpeech_TTS_zh-CN_HuiHui.msi" ^(
echo     echo [2/2] 安装中文语音包（慧慧）...
echo     msiexec /i "MSSpeech_TTS_zh-CN_HuiHui.msi" /quiet /norestart
echo     echo [✓] 语音包安装完成
echo ^)
echo.
echo if exist "MSSpeech_TTS_zh-CN_Kangkang.msi" ^(
echo     echo [可选] 安装中文语音包（康康）...
echo     msiexec /i "MSSpeech_TTS_zh-CN_Kangkang.msi" /quiet /norestart
echo     echo [✓] 康康语音包安装完成
echo ^)
echo.
echo echo ========================================
echo echo 安装完成！
echo echo ========================================
echo echo.
echo echo ⚠️ 重要：必须重启计算机才能生效！
echo echo.
echo set /p restart="是否现在重启计算机? ^(Y/N^): "
echo if /i "%%restart%%"=="Y" ^(
echo     shutdown /r /t 10 /c "TTS组件安装完成，重启生效"
echo     echo 系统将在10秒后重启...
echo ^) else ^(
echo     echo 请稍后手动重启计算机
echo ^)
echo.
echo pause
) > "%DOWNLOAD_DIR%\快速安装.bat"

:: 创建简单说明
(
echo TTS组件安装包
echo ================
echo.
echo 📁 包含文件：
echo - SpeechPlatformRuntime.msi ^(语音平台^)
echo - MSSpeech_TTS_zh-CN_HuiHui.msi ^(女声^)
echo - MSSpeech_TTS_zh-CN_Kangkang.msi ^(男声^)
echo - 快速安装.bat ^(一键安装^)
echo.
echo 🚀 安装方法：
echo 1. 右键点击"快速安装.bat"
echo 2. 选择"以管理员身份运行"
echo 3. 等待安装完成
echo 4. 重启计算机
echo.
echo ✅ 安装后：
echo 重启计算机后，"我要记忆"程序的语音功能就可以正常使用了！
) > "%DOWNLOAD_DIR%\安装说明.txt"

echo.
echo ========================================
echo 下载完成！
echo ========================================
echo.

if %DOWNLOADED_COUNT% equ %FILES_TO_DOWNLOAD% (
    echo [✅] 所有文件下载成功！ ^(%DOWNLOADED_COUNT%/%FILES_TO_DOWNLOAD%^)
) else (
    echo [⚠️] 部分文件下载失败 ^(%DOWNLOADED_COUNT%/%FILES_TO_DOWNLOAD%^)
    echo 请检查网络连接或稍后重试
)

echo.
echo 📁 文件保存在: %DOWNLOAD_DIR%
echo.
echo 📋 下载的文件：
dir "%DOWNLOAD_DIR%" /b

echo.
echo 🎯 下一步：
echo 1. 进入 "%DOWNLOAD_DIR%" 文件夹
echo 2. 右键点击"快速安装.bat"
echo 3. 选择"以管理员身份运行"
echo.

set /p open_folder="是否现在打开文件夹? (Y/N): "
if /i "%open_folder%"=="Y" (
    explorer "%DOWNLOAD_DIR%"
)

echo.
echo 🎉 下载工具执行完成！
echo 感谢使用TTS组件下载工具
pause
