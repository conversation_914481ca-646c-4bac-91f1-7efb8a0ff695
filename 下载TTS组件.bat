@echo off
setlocal enabledelayedexpansion

echo ========================================
echo TTS 组件自动下载工具 v2.0
echo ========================================
echo 自动下载Windows 7所需的TTS组件并保存到本地
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [提示] 建议以管理员身份运行以获得最佳体验
    echo 当前将以普通用户权限继续...
    echo.
)

:: 创建下载目录
set "DOWNLOAD_DIR=%~dp0TTS组件"
if not exist "%DOWNLOAD_DIR%" (
    mkdir "%DOWNLOAD_DIR%"
    echo [✓] 创建下载目录: %DOWNLOAD_DIR%
) else (
    echo [✓] 下载目录已存在: %DOWNLOAD_DIR%
)
echo.

:: 定义下载信息
echo [1] 准备下载组件信息
echo ========================================

:: Microsoft Speech Platform Runtime 11.0 (x64)
set "RUNTIME_URL=https://download.microsoft.com/download/3/4/9/349B0943-F0D6-4B1F-9259-4207B4B8F78A/SpeechPlatformRuntime.msi"
set "RUNTIME_FILE=%DOWNLOAD_DIR%\SpeechPlatformRuntime.msi"
set "RUNTIME_SIZE=约 5MB"

:: 中文语音包 - 慧慧（女声）
set "HUIHUI_URL=https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_HuiHui.msi"
set "HUIHUI_FILE=%DOWNLOAD_DIR%\MSSpeech_TTS_zh-CN_HuiHui.msi"
set "HUIHUI_SIZE=约 60MB"

:: 中文语音包 - 康康（男声）
set "KANGKANG_URL=https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_Kangkang.msi"
set "KANGKANG_FILE=%DOWNLOAD_DIR%\MSSpeech_TTS_zh-CN_Kangkang.msi"
set "KANGKANG_SIZE=约 60MB"

echo 将要下载的组件：
echo.
echo 1. Microsoft Speech Platform Runtime 11.0 (x64)
echo    文件名: SpeechPlatformRuntime.msi
echo    大小: %RUNTIME_SIZE%
echo    说明: 语音平台核心运行时（必需）
echo.
echo 2. 中文语音包 - 慧慧（女声）
echo    文件名: MSSpeech_TTS_zh-CN_HuiHui.msi
echo    大小: %HUIHUI_SIZE%
echo    说明: 中文女声语音合成包（推荐）
echo.
echo 3. 中文语音包 - 康康（男声）
echo    文件名: MSSpeech_TTS_zh-CN_Kangkang.msi
echo    大小: %KANGKANG_SIZE%
echo    说明: 中文男声语音合成包（可选）
echo.

:: 检查现有文件
echo [2] 检查现有文件
echo ========================================

set "RUNTIME_EXISTS=0"
set "HUIHUI_EXISTS=0"
set "KANGKANG_EXISTS=0"

if exist "%RUNTIME_FILE%" (
    echo [✓] Speech Platform Runtime 已存在
    set "RUNTIME_EXISTS=1"
) else (
    echo [!] Speech Platform Runtime 需要下载
)

if exist "%HUIHUI_FILE%" (
    echo [✓] 中文语音包（慧慧）已存在
    set "HUIHUI_EXISTS=1"
) else (
    echo [!] 中文语音包（慧慧）需要下载
)

if exist "%KANGKANG_FILE%" (
    echo [✓] 中文语音包（康康）已存在
    set "KANGKANG_EXISTS=1"
) else (
    echo [!] 中文语音包（康康）需要下载
)

echo.

:: 用户选择下载项目
echo [3] 选择下载项目
echo ========================================

if "%RUNTIME_EXISTS%"=="1" and "%HUIHUI_EXISTS%"=="1" and "%KANGKANG_EXISTS%"=="1" (
    echo [信息] 所有组件都已存在！
    echo.
    set /p redownload="是否重新下载所有组件? (Y/N): "
    if /i not "!redownload!"=="Y" goto create_installer
    set "RUNTIME_EXISTS=0"
    set "HUIHUI_EXISTS=0"
    set "KANGKANG_EXISTS=0"
)

echo 请选择要下载的组件：
echo.
echo 1. 仅下载必需组件（Runtime + 慧慧语音包）
echo 2. 下载所有组件（Runtime + 两个语音包）
echo 3. 自定义选择
echo 4. 跳过下载，仅创建安装脚本
echo.

set /p choice="请输入选择 (1-4) [默认: 1]: "
if "%choice%"=="" set choice=1

if "%choice%"=="1" (
    echo [选择] 下载必需组件
    set "DOWNLOAD_RUNTIME=1"
    set "DOWNLOAD_HUIHUI=1"
    set "DOWNLOAD_KANGKANG=0"
) else if "%choice%"=="2" (
    echo [选择] 下载所有组件
    set "DOWNLOAD_RUNTIME=1"
    set "DOWNLOAD_HUIHUI=1"
    set "DOWNLOAD_KANGKANG=1"
) else if "%choice%"=="3" (
    echo [选择] 自定义选择
    
    if "%RUNTIME_EXISTS%"=="0" (
        set /p dl_rt="下载 Speech Platform Runtime? (Y/N) [Y]: "
        if /i "!dl_rt!"=="N" (set "DOWNLOAD_RUNTIME=0") else (set "DOWNLOAD_RUNTIME=1")
    ) else (
        set "DOWNLOAD_RUNTIME=0"
    )
    
    if "%HUIHUI_EXISTS%"=="0" (
        set /p dl_hh="下载中文语音包（慧慧-女声）? (Y/N) [Y]: "
        if /i "!dl_hh!"=="N" (set "DOWNLOAD_HUIHUI=0") else (set "DOWNLOAD_HUIHUI=1")
    ) else (
        set "DOWNLOAD_HUIHUI=0"
    )
    
    if "%KANGKANG_EXISTS%"=="0" (
        set /p dl_kk="下载中文语音包（康康-男声）? (Y/N) [N]: "
        if /i "!dl_kk!"=="Y" (set "DOWNLOAD_KANGKANG=1") else (set "DOWNLOAD_KANGKANG=0")
    ) else (
        set "DOWNLOAD_KANGKANG=0"
    )
) else if "%choice%"=="4" (
    echo [选择] 跳过下载
    set "DOWNLOAD_RUNTIME=0"
    set "DOWNLOAD_HUIHUI=0"
    set "DOWNLOAD_KANGKANG=0"
    goto create_installer
) else (
    echo [错误] 无效选择，使用默认选项
    set "DOWNLOAD_RUNTIME=1"
    set "DOWNLOAD_HUIHUI=1"
    set "DOWNLOAD_KANGKANG=0"
)

echo.

:: 开始下载
echo [4] 开始下载组件
echo ========================================

set "DOWNLOAD_SUCCESS=1"
set "TOTAL_DOWNLOADS=0"
set "SUCCESSFUL_DOWNLOADS=0"

:: 下载 Speech Platform Runtime
if "%DOWNLOAD_RUNTIME%"=="1" (
    set /a TOTAL_DOWNLOADS+=1
    echo.
    echo 正在下载 Speech Platform Runtime...
    echo 文件大小: %RUNTIME_SIZE%
    echo 下载地址: %RUNTIME_URL%
    echo 保存位置: %RUNTIME_FILE%
    echo.
    
    powershell -Command "& {try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Write-Host '开始下载...'; Invoke-WebRequest -Uri '%RUNTIME_URL%' -OutFile '%RUNTIME_FILE%' -UseBasicParsing -TimeoutSec 300; Write-Host '下载完成！' } catch { Write-Host '下载失败:' $_.Exception.Message; exit 1 }}"
    
    if !errorlevel! equ 0 (
        if exist "%RUNTIME_FILE%" (
            echo [✓] Speech Platform Runtime 下载成功
            set /a SUCCESSFUL_DOWNLOADS+=1
        ) else (
            echo [✗] Speech Platform Runtime 下载失败 - 文件不存在
            set "DOWNLOAD_SUCCESS=0"
        )
    ) else (
        echo [✗] Speech Platform Runtime 下载失败
        set "DOWNLOAD_SUCCESS=0"
    )
)

:: 下载中文语音包（慧慧）
if "%DOWNLOAD_HUIHUI%"=="1" (
    set /a TOTAL_DOWNLOADS+=1
    echo.
    echo 正在下载中文语音包（慧慧-女声）...
    echo 文件大小: %HUIHUI_SIZE%
    echo 下载地址: %HUIHUI_URL%
    echo 保存位置: %HUIHUI_FILE%
    echo 请耐心等待，文件较大...
    echo.
    
    powershell -Command "& {try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Write-Host '开始下载...'; Invoke-WebRequest -Uri '%HUIHUI_URL%' -OutFile '%HUIHUI_FILE%' -UseBasicParsing -TimeoutSec 600; Write-Host '下载完成！' } catch { Write-Host '下载失败:' $_.Exception.Message; exit 1 }}"
    
    if !errorlevel! equ 0 (
        if exist "%HUIHUI_FILE%" (
            echo [✓] 中文语音包（慧慧）下载成功
            set /a SUCCESSFUL_DOWNLOADS+=1
        ) else (
            echo [✗] 中文语音包（慧慧）下载失败 - 文件不存在
            set "DOWNLOAD_SUCCESS=0"
        )
    ) else (
        echo [✗] 中文语音包（慧慧）下载失败
        set "DOWNLOAD_SUCCESS=0"
    )
)

:: 下载中文语音包（康康）
if "%DOWNLOAD_KANGKANG%"=="1" (
    set /a TOTAL_DOWNLOADS+=1
    echo.
    echo 正在下载中文语音包（康康-男声）...
    echo 文件大小: %KANGKANG_SIZE%
    echo 下载地址: %KANGKANG_URL%
    echo 保存位置: %KANGKANG_FILE%
    echo 请耐心等待，文件较大...
    echo.
    
    powershell -Command "& {try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Write-Host '开始下载...'; Invoke-WebRequest -Uri '%KANGKANG_URL%' -OutFile '%KANGKANG_FILE%' -UseBasicParsing -TimeoutSec 600; Write-Host '下载完成！' } catch { Write-Host '下载失败:' $_.Exception.Message; exit 1 }}"
    
    if !errorlevel! equ 0 (
        if exist "%KANGKANG_FILE%" (
            echo [✓] 中文语音包（康康）下载成功
            set /a SUCCESSFUL_DOWNLOADS+=1
        ) else (
            echo [✗] 中文语音包（康康）下载失败 - 文件不存在
            set "DOWNLOAD_SUCCESS=0"
        )
    ) else (
        echo [✗] 中文语音包（康康）下载失败
        set "DOWNLOAD_SUCCESS=0"
    )
)

:create_installer
echo.
echo [5] 创建安装脚本
echo ========================================

:: 创建自动安装脚本
echo 正在创建自动安装脚本...

(
echo @echo off
echo setlocal enabledelayedexpansion
echo.
echo echo ========================================
echo echo TTS 组件自动安装器
echo echo ========================================
echo echo 从本地文件安装TTS组件
echo echo.
echo.
echo :: 检查管理员权限
echo net session ^>nul 2^>^&1
echo if %%errorlevel%% neq 0 ^(
echo     echo [错误] 需要管理员权限
echo     echo 请右键点击此文件，选择"以管理员身份运行"
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo [✓] 管理员权限确认
echo echo.
echo.
echo :: 检查文件是否存在
echo set "INSTALL_DIR=%%~dp0"
echo set "RUNTIME_FILE=%%INSTALL_DIR%%SpeechPlatformRuntime.msi"
echo set "HUIHUI_FILE=%%INSTALL_DIR%%MSSpeech_TTS_zh-CN_HuiHui.msi"
echo set "KANGKANG_FILE=%%INSTALL_DIR%%MSSpeech_TTS_zh-CN_Kangkang.msi"
echo.
echo echo [1] 检查安装文件
echo echo ========================================
echo.
echo if exist "%%RUNTIME_FILE%%" ^(
echo     echo [✓] 找到 Speech Platform Runtime
echo     set "HAS_RUNTIME=1"
echo ^) else ^(
echo     echo [!] 未找到 Speech Platform Runtime
echo     set "HAS_RUNTIME=0"
echo ^)
echo.
echo if exist "%%HUIHUI_FILE%%" ^(
echo     echo [✓] 找到中文语音包（慧慧）
echo     set "HAS_HUIHUI=1"
echo ^) else ^(
echo     echo [!] 未找到中文语音包（慧慧）
echo     set "HAS_HUIHUI=0"
echo ^)
echo.
echo if exist "%%KANGKANG_FILE%%" ^(
echo     echo [✓] 找到中文语音包（康康）
echo     set "HAS_KANGKANG=1"
echo ^) else ^(
echo     echo [!] 未找到中文语音包（康康）
echo     set "HAS_KANGKANG=0"
echo ^)
echo.
echo echo.
echo echo [2] 开始安装
echo echo ========================================
echo.
echo :: 安装 Speech Platform Runtime
echo if "%%HAS_RUNTIME%%"=="1" ^(
echo     echo 正在安装 Speech Platform Runtime...
echo     start /wait msiexec /i "%%RUNTIME_FILE%%" /quiet /norestart
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] Speech Platform Runtime 安装成功
echo     ^) else ^(
echo         echo [✗] Speech Platform Runtime 安装失败
echo     ^)
echo     echo.
echo ^)
echo.
echo :: 安装语音包
echo if "%%HAS_HUIHUI%%"=="1" ^(
echo     echo 正在安装中文语音包（慧慧）...
echo     start /wait msiexec /i "%%HUIHUI_FILE%%" /quiet /norestart
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] 中文语音包（慧慧）安装成功
echo     ^) else ^(
echo         echo [✗] 中文语音包（慧慧）安装失败
echo     ^)
echo     echo.
echo ^)
echo.
echo if "%%HAS_KANGKANG%%"=="1" ^(
echo     echo 正在安装中文语音包（康康）...
echo     start /wait msiexec /i "%%KANGKANG_FILE%%" /quiet /norestart
echo     if ^^!errorlevel^^! equ 0 ^(
echo         echo [✓] 中文语音包（康康）安装成功
echo     ^) else ^(
echo         echo [✗] 中文语音包（康康）安装失败
echo     ^)
echo     echo.
echo ^)
echo.
echo echo [3] 安装完成
echo echo ========================================
echo echo.
echo echo 重要提醒：
echo echo 1. 必须重启计算机才能使TTS功能生效
echo echo 2. 重启后重新运行"我要记忆"程序
echo echo.
echo set /p restart_now="是否现在重启计算机? ^(Y/N^): "
echo if /i "%%restart_now%%"=="Y" ^(
echo     shutdown /r /t 10 /c "TTS组件安装完成，重启系统"
echo ^) else ^(
echo     echo 请记得稍后手动重启计算机
echo ^)
echo.
echo pause
) > "%DOWNLOAD_DIR%\安装TTS组件.bat"

echo [✓] 安装脚本创建完成: %DOWNLOAD_DIR%\安装TTS组件.bat

:: 创建说明文件
(
echo ========================================
echo TTS 组件安装包说明
echo ========================================
echo.
echo 此文件夹包含Windows 7系统所需的TTS组件：
echo.
echo 📁 文件清单：
echo.
if exist "%RUNTIME_FILE%" echo ✓ SpeechPlatformRuntime.msi - 语音平台核心运行时
if exist "%HUIHUI_FILE%" echo ✓ MSSpeech_TTS_zh-CN_HuiHui.msi - 中文语音包（慧慧-女声）
if exist "%KANGKANG_FILE%" echo ✓ MSSpeech_TTS_zh-CN_Kangkang.msi - 中文语音包（康康-男声）
echo ✓ 安装TTS组件.bat - 自动安装脚本
echo ✓ 使用说明.txt - 本文件
echo.
echo 🚀 快速安装：
echo.
echo 1. 右键点击"安装TTS组件.bat"
echo 2. 选择"以管理员身份运行"
echo 3. 按照提示完成安装
echo 4. 重启计算机
echo.
echo 📋 手动安装顺序：
echo.
echo 1. 先安装 SpeechPlatformRuntime.msi
echo 2. 再安装语音包（.msi文件）
echo 3. 重启计算机
echo.
echo ⚠️ 重要提醒：
echo.
echo - 必须以管理员身份运行安装程序
echo - 安装完成后必须重启计算机
echo - 重启后重新运行"我要记忆"程序
echo.
echo 🔧 故障排除：
echo.
echo 如果安装后仍然无法使用语音功能：
echo 1. 确认已重启计算机
echo 2. 检查Windows Audio服务是否启动
echo 3. 运行程序目录下的诊断工具
echo.
echo ========================================
) > "%DOWNLOAD_DIR%\使用说明.txt"

echo [✓] 说明文件创建完成: %DOWNLOAD_DIR%\使用说明.txt

echo.
echo [6] 下载完成
echo ========================================

if %TOTAL_DOWNLOADS% gtr 0 (
    echo 下载统计：
    echo - 总计尝试下载: %TOTAL_DOWNLOADS% 个文件
    echo - 成功下载: %SUCCESSFUL_DOWNLOADS% 个文件
    echo.
    
    if %SUCCESSFUL_DOWNLOADS% equ %TOTAL_DOWNLOADS% (
        echo [✓] 所有文件下载成功！
    ) else (
        echo [!] 部分文件下载失败
        echo 请检查网络连接或手动下载缺失的文件
    )
) else (
    echo [信息] 未执行下载操作
)

echo.
echo 📁 文件保存位置: %DOWNLOAD_DIR%
echo.
echo 📋 文件夹内容：
dir "%DOWNLOAD_DIR%" /b

echo.
echo 🚀 下一步操作：
echo.
echo 1. 进入文件夹: %DOWNLOAD_DIR%
echo 2. 右键点击"安装TTS组件.bat"
echo 3. 选择"以管理员身份运行"
echo 4. 按照提示完成安装
echo.

set /p open_folder="是否现在打开文件夹? (Y/N): "
if /i "%open_folder%"=="Y" (
    explorer "%DOWNLOAD_DIR%"
)

echo.
echo 下载工具执行完成！
pause
