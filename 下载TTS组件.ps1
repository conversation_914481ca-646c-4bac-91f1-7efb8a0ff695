# TTS组件下载工具 PowerShell版本
# 提供更好的下载进度显示和错误处理

param(
    [string]$DownloadPath = "TTS组件",
    [switch]$SkipConfirm
)

# 设置控制台标题和编码
$Host.UI.RawUI.WindowTitle = "TTS组件下载工具"
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "TTS 组件下载工具 PowerShell版" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "提供进度显示的高级下载工具" -ForegroundColor Green
Write-Host ""

# 检查PowerShell版本
if ($PSVersionTable.PSVersion.Major -lt 3) {
    Write-Host "[错误] 需要PowerShell 3.0或更高版本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 创建下载目录
$DownloadDir = Join-Path $PSScriptRoot $DownloadPath
if (-not (Test-Path $DownloadDir)) {
    New-Item -ItemType Directory -Path $DownloadDir -Force | Out-Null
    Write-Host "[✓] 创建下载目录: $DownloadDir" -ForegroundColor Green
} else {
    Write-Host "[✓] 下载目录已存在: $DownloadDir" -ForegroundColor Green
}
Write-Host ""

# 定义下载文件信息
$Downloads = @(
    @{
        Name = "Microsoft Speech Platform Runtime 11.0"
        Url = "https://download.microsoft.com/download/3/4/9/349B0943-F0D6-4B1F-9259-4207B4B8F78A/SpeechPlatformRuntime.msi"
        FileName = "SpeechPlatformRuntime.msi"
        Size = "约 5MB"
        Description = "语音平台核心运行时（必需）"
        Required = $true
    },
    @{
        Name = "中文语音包 - 慧慧（女声）"
        Url = "https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_HuiHui.msi"
        FileName = "MSSpeech_TTS_zh-CN_HuiHui.msi"
        Size = "约 60MB"
        Description = "中文女声语音合成包（推荐）"
        Required = $true
    },
    @{
        Name = "中文语音包 - 康康（男声）"
        Url = "https://download.microsoft.com/download/6/9/3/6939E592-3036-4BD0-8C83-BCD3A07C4C8A/MSSpeech_TTS_zh-CN_Kangkang.msi"
        FileName = "MSSpeech_TTS_zh-CN_Kangkang.msi"
        Size = "约 60MB"
        Description = "中文男声语音合成包（可选）"
        Required = $false
    }
)

# 显示下载列表
Write-Host "📦 将要下载的组件：" -ForegroundColor Yellow
Write-Host ""
for ($i = 0; $i -lt $Downloads.Count; $i++) {
    $item = $Downloads[$i]
    $status = if ($item.Required) { "必需" } else { "可选" }
    Write-Host "  $($i+1). $($item.Name)" -ForegroundColor White
    Write-Host "     文件名: $($item.FileName)" -ForegroundColor Gray
    Write-Host "     大小: $($item.Size)" -ForegroundColor Gray
    Write-Host "     说明: $($item.Description) [$status]" -ForegroundColor Gray
    Write-Host ""
}

# 检查现有文件
Write-Host "🔍 检查现有文件：" -ForegroundColor Yellow
$ExistingFiles = 0
foreach ($item in $Downloads) {
    $FilePath = Join-Path $DownloadDir $item.FileName
    if (Test-Path $FilePath) {
        Write-Host "  [✓] $($item.FileName) 已存在" -ForegroundColor Green
        $ExistingFiles++
    } else {
        Write-Host "  [!] $($item.FileName) 需要下载" -ForegroundColor Yellow
    }
}
Write-Host ""

# 用户确认
if (-not $SkipConfirm) {
    if ($ExistingFiles -eq $Downloads.Count) {
        $Response = Read-Host "所有文件都已存在。是否重新下载？(Y/N) [N]"
        if ($Response -ne "Y" -and $Response -ne "y") {
            Write-Host "跳过下载，直接创建安装脚本..." -ForegroundColor Yellow
            $SkipDownload = $true
        }
    } else {
        $Response = Read-Host "确认开始下载？(Y/N) [Y]"
        if ($Response -eq "N" -or $Response -eq "n") {
            Write-Host "用户取消下载" -ForegroundColor Yellow
            exit 0
        }
    }
}

# 下载函数
function Download-FileWithProgress {
    param(
        [string]$Url,
        [string]$OutputPath,
        [string]$FileName
    )
    
    try {
        Write-Host "  正在下载: $FileName" -ForegroundColor Cyan
        Write-Host "  URL: $Url" -ForegroundColor Gray
        Write-Host "  保存到: $OutputPath" -ForegroundColor Gray
        
        # 设置安全协议
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        
        # 创建WebClient并设置进度回调
        $WebClient = New-Object System.Net.WebClient
        
        # 注册进度事件
        Register-ObjectEvent -InputObject $WebClient -EventName DownloadProgressChanged -Action {
            $Global:DownloadProgress = $Event.SourceEventArgs.ProgressPercentage
            $Global:BytesReceived = $Event.SourceEventArgs.BytesReceived
            $Global:TotalBytes = $Event.SourceEventArgs.TotalBytesToReceive
        } | Out-Null
        
        # 开始下载
        $WebClient.DownloadFileAsync($Url, $OutputPath)
        
        # 显示进度
        $LastProgress = -1
        while ($WebClient.IsBusy) {
            if ($Global:DownloadProgress -ne $LastProgress) {
                $LastProgress = $Global:DownloadProgress
                if ($Global:TotalBytes -gt 0) {
                    $MB_Received = [math]::Round($Global:BytesReceived / 1MB, 2)
                    $MB_Total = [math]::Round($Global:TotalBytes / 1MB, 2)
                    Write-Progress -Activity "下载 $FileName" -Status "$MB_Received MB / $MB_Total MB" -PercentComplete $Global:DownloadProgress
                }
            }
            Start-Sleep -Milliseconds 100
        }
        
        Write-Progress -Activity "下载 $FileName" -Completed
        $WebClient.Dispose()
        
        if (Test-Path $OutputPath) {
            $FileSize = [math]::Round((Get-Item $OutputPath).Length / 1MB, 2)
            Write-Host "  [✓] 下载成功！文件大小: $FileSize MB" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  [✗] 下载失败：文件不存在" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "  [✗] 下载失败：$($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 开始下载
if (-not $SkipDownload) {
    Write-Host "🚀 开始下载组件..." -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    
    $SuccessCount = 0
    $TotalCount = $Downloads.Count
    
    for ($i = 0; $i -lt $Downloads.Count; $i++) {
        $item = $Downloads[$i]
        $FilePath = Join-Path $DownloadDir $item.FileName
        
        Write-Host ""
        Write-Host "[$($i+1)/$TotalCount] $($item.Name)" -ForegroundColor White
        
        if (Test-Path $FilePath) {
            Write-Host "  [跳过] 文件已存在" -ForegroundColor Yellow
            $SuccessCount++
        } else {
            if (Download-FileWithProgress -Url $item.Url -OutputPath $FilePath -FileName $item.FileName) {
                $SuccessCount++
            }
        }
    }
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "下载完成！成功: $SuccessCount/$TotalCount" -ForegroundColor $(if ($SuccessCount -eq $TotalCount) { "Green" } else { "Yellow" })
}

# 创建安装脚本
Write-Host ""
Write-Host "📝 创建安装脚本..." -ForegroundColor Yellow

$InstallScript = @"
@echo off
setlocal enabledelayedexpansion
title TTS组件安装器

echo ========================================
echo TTS 组件安装器
echo ========================================
echo 自动安装Windows 7 TTS组件
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要管理员权限！
    echo.
    echo 请按以下步骤操作：
    echo 1. 右键点击此文件
    echo 2. 选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo [✓] 管理员权限确认
echo.

echo [1] 检查安装文件
echo ========================================

set "INSTALL_SUCCESS=0"

if exist "SpeechPlatformRuntime.msi" (
    echo [✓] Speech Platform Runtime 文件存在
    echo [1/2] 正在安装 Speech Platform Runtime...
    msiexec /i "SpeechPlatformRuntime.msi" /quiet /norestart
    if !errorlevel! equ 0 (
        echo [✓] Speech Platform Runtime 安装成功
        set /a INSTALL_SUCCESS+=1
    ) else (
        echo [✗] Speech Platform Runtime 安装失败
    )
    echo.
) else (
    echo [!] 未找到 SpeechPlatformRuntime.msi
)

if exist "MSSpeech_TTS_zh-CN_HuiHui.msi" (
    echo [✓] 中文语音包（慧慧）文件存在
    echo [2/2] 正在安装中文语音包（慧慧）...
    msiexec /i "MSSpeech_TTS_zh-CN_HuiHui.msi" /quiet /norestart
    if !errorlevel! equ 0 (
        echo [✓] 中文语音包（慧慧）安装成功
        set /a INSTALL_SUCCESS+=1
    ) else (
        echo [✗] 中文语音包（慧慧）安装失败
    )
    echo.
) else (
    echo [!] 未找到 MSSpeech_TTS_zh-CN_HuiHui.msi
)

if exist "MSSpeech_TTS_zh-CN_Kangkang.msi" (
    echo [✓] 中文语音包（康康）文件存在
    echo [可选] 正在安装中文语音包（康康）...
    msiexec /i "MSSpeech_TTS_zh-CN_Kangkang.msi" /quiet /norestart
    if !errorlevel! equ 0 (
        echo [✓] 中文语音包（康康）安装成功
    ) else (
        echo [✗] 中文语音包（康康）安装失败
    )
    echo.
)

echo ========================================
echo 安装完成
echo ========================================
echo.

if %INSTALL_SUCCESS% geq 2 (
    echo [✅] 核心组件安装成功！
) else (
    echo [⚠️] 部分组件安装失败，可能影响功能
)

echo.
echo ⚠️ 重要提醒：
echo 1. 必须重启计算机才能使TTS功能生效
echo 2. 重启后重新运行"我要记忆"程序
echo 3. 如果仍有问题，请运行诊断工具
echo.

set /p restart="是否现在重启计算机? (Y/N): "
if /i "%restart%"=="Y" (
    echo 系统将在10秒后重启...
    shutdown /r /t 10 /c "TTS组件安装完成，重启使更改生效"
) else (
    echo 请记得稍后手动重启计算机
)

echo.
pause
"@

$InstallScriptPath = Join-Path $DownloadDir "安装TTS组件.bat"
$InstallScript | Out-File -FilePath $InstallScriptPath -Encoding Default
Write-Host "[✓] 安装脚本创建完成: 安装TTS组件.bat" -ForegroundColor Green

# 创建说明文件
$ReadmeContent = @"
TTS组件安装包使用说明
========================

📁 文件清单：
$(foreach ($item in $Downloads) {
    $FilePath = Join-Path $DownloadDir $item.FileName
    if (Test-Path $FilePath) {
        "✓ $($item.FileName) - $($item.Description)"
    } else {
        "✗ $($item.FileName) - 未下载"
    }
})
✓ 安装TTS组件.bat - 自动安装脚本
✓ 使用说明.txt - 本文件

🚀 快速安装：
1. 右键点击"安装TTS组件.bat"
2. 选择"以管理员身份运行"
3. 等待安装完成
4. 重启计算机

📋 手动安装顺序：
1. 先安装 SpeechPlatformRuntime.msi
2. 再安装语音包文件
3. 重启计算机

⚠️ 重要提醒：
- 必须以管理员身份运行安装程序
- 安装完成后必须重启计算机
- 重启后"我要记忆"程序的语音功能即可正常使用

🔧 故障排除：
如果安装后仍无法使用语音功能：
1. 确认已重启计算机
2. 检查Windows Audio服务是否启动
3. 运行程序目录下的诊断工具
4. 查看Windows事件日志

📞 技术支持：
- 运行 Windows7-Diagnostic.bat 进行系统诊断
- 查看程序日志文件获取详细错误信息
- 确保系统满足最低要求（Windows 7 SP1 + .NET Framework 4.0+）
"@

$ReadmePath = Join-Path $DownloadDir "使用说明.txt"
$ReadmeContent | Out-File -FilePath $ReadmePath -Encoding Default
Write-Host "[✓] 说明文件创建完成: 使用说明.txt" -ForegroundColor Green

# 显示结果
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "任务完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📁 文件保存位置: $DownloadDir" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 文件夹内容：" -ForegroundColor Yellow
Get-ChildItem $DownloadDir | ForEach-Object {
    $Size = if ($_.PSIsContainer) { "<DIR>" } else { "$([math]::Round($_.Length / 1MB, 2)) MB" }
    Write-Host "  $($_.Name) ($Size)" -ForegroundColor White
}

Write-Host ""
Write-Host "🎯 下一步操作：" -ForegroundColor Yellow
Write-Host "1. 进入文件夹: $DownloadDir" -ForegroundColor White
Write-Host "2. 右键点击'安装TTS组件.bat'" -ForegroundColor White
Write-Host "3. 选择'以管理员身份运行'" -ForegroundColor White
Write-Host "4. 按照提示完成安装" -ForegroundColor White
Write-Host ""

$OpenFolder = Read-Host "是否现在打开文件夹? (Y/N)"
if ($OpenFolder -eq "Y" -or $OpenFolder -eq "y") {
    Start-Process explorer.exe -ArgumentList $DownloadDir
}

Write-Host ""
Write-Host "🎉 TTS组件下载工具执行完成！" -ForegroundColor Green
Write-Host "感谢使用！" -ForegroundColor Cyan
Read-Host "按任意键退出"
