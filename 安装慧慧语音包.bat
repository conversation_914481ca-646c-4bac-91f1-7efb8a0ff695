@echo off
setlocal enabledelayedexpansion

title 慧慧语音包安装器

echo ========================================
echo 慧慧语音包安装器 v1.0
echo ========================================
echo 专门安装从博客园下载的修改版慧慧语音包
echo 支持Windows 7及以上系统
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 需要管理员权限！
    echo.
    echo 请按以下步骤操作：
    echo 1. 右键点击此文件
    echo 2. 选择"以管理员身份运行"
    echo 3. 在UAC提示中点击"是"
    echo.
    pause
    exit /b 1
)

echo [✓] 管理员权限确认
echo.

:: 检查操作系统版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo [信息] 系统版本: %VERSION%
if "%VERSION%"=="6.1" (
    echo [✓] Windows 7 系统确认
) else if "%VERSION%"=="6.2" (
    echo [✓] Windows 8 系统确认
) else if "%VERSION%"=="6.3" (
    echo [✓] Windows 8.1 系统确认
) else if "%VERSION%"=="10.0" (
    echo [✓] Windows 10/11 系统确认
) else (
    echo [!] 未知系统版本，继续安装...
)
echo.

echo [1] 查找慧慧语音包文件
echo ========================================

:: 查找可能的慧慧语音包文件
set "HUIHUI_FILE="
set "RUNTIME_FILE="

:: 查找慧慧语音包
if exist "MSSpeech_TTS_zh-CN_HuiHui.msi" (
    set "HUIHUI_FILE=MSSpeech_TTS_zh-CN_HuiHui.msi"
    echo [✓] 找到慧慧语音包: MSSpeech_TTS_zh-CN_HuiHui.msi
) else if exist "huihui.msi" (
    set "HUIHUI_FILE=huihui.msi"
    echo [✓] 找到慧慧语音包: huihui.msi
) else (
    echo [!] 未找到慧慧语音包文件
    echo.
    echo 请确保以下文件之一存在于当前目录：
    echo • MSSpeech_TTS_zh-CN_HuiHui.msi
    echo • huihui.msi
    echo.
    echo 如果文件名不同，请重命名为上述名称之一
    pause
    exit /b 1
)

:: 查找Speech Platform Runtime
if exist "SpeechPlatformRuntime.msi" (
    set "RUNTIME_FILE=SpeechPlatformRuntime.msi"
    echo [✓] 找到Speech Platform Runtime: SpeechPlatformRuntime.msi
) else (
    echo [!] 未找到Speech Platform Runtime文件
    echo [提示] 如果没有此文件，将尝试仅安装语音包
)

echo.

echo [2] 系统诊断检查
echo ========================================

:: 检查现有TTS组件
echo 正在检查现有TTS组件...

:: 检查SAPI组件
if exist "%SystemRoot%\System32\Speech\Common\sapi.dll" (
    echo [✓] SAPI (64位) 已安装
    set "SAPI_64_OK=1"
) else (
    echo [!] SAPI (64位) 未安装
    set "SAPI_64_OK=0"
)

if exist "%SystemRoot%\SysWOW64\Speech\Common\sapi.dll" (
    echo [✓] SAPI (32位) 已安装
    set "SAPI_32_OK=1"
) else (
    echo [!] SAPI (32位) 未安装
    set "SAPI_32_OK=0"
)

:: 检查现有语音包
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" >nul 2>&1
if %errorlevel% equ 0 (
    for /f %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^| find /c "HKEY"') do (
        set "VOICE_COUNT=%%i"
    )
    echo [✓] 检测到 !VOICE_COUNT! 个现有语音包
) else (
    echo [!] 未检测到现有语音包
    set "VOICE_COUNT=0"
)

:: 检查Windows Audio服务
sc query AudioSrv | find "RUNNING" >nul
if %errorlevel% equ 0 (
    echo [✓] Windows Audio 服务正在运行
) else (
    echo [!] Windows Audio 服务未运行，正在启动...
    sc start AudioSrv >nul 2>&1
    if %errorlevel% equ 0 (
        echo [✓] Windows Audio 服务启动成功
    ) else (
        echo [✗] Windows Audio 服务启动失败
    )
)

echo.

echo [3] 开始安装
echo ========================================

:: 安装Speech Platform Runtime（如果存在）
if not "%RUNTIME_FILE%"=="" (
    echo 步骤 1: 安装 Speech Platform Runtime
    echo ----------------------------------------
    echo 正在安装 Speech Platform Runtime...
    echo 这是语音平台的核心运行时组件
    echo.
    
    start /wait msiexec /i "%RUNTIME_FILE%" /quiet /norestart
    
    if !errorlevel! equ 0 (
        echo [✓] Speech Platform Runtime 安装成功
    ) else (
        echo [✗] Speech Platform Runtime 安装失败 (错误代码: !errorlevel!)
        echo 可能原因：权限不足、文件损坏或系统不兼容
        echo 继续安装语音包...
    )
    echo.
)

:: 安装慧慧语音包
echo 步骤 2: 安装慧慧语音包
echo ----------------------------------------
echo 正在安装慧慧中文语音包...
echo 这是高质量的中文女声语音包
echo.

start /wait msiexec /i "%HUIHUI_FILE%" /quiet /norestart

if !errorlevel! equ 0 (
    echo [✓] 慧慧语音包安装成功
    set "INSTALL_SUCCESS=1"
) else (
    echo [✗] 慧慧语音包安装失败 (错误代码: !errorlevel!)
    echo.
    echo 可能的原因：
    echo 1. 权限不足
    echo 2. 缺少Speech Platform Runtime
    echo 3. 文件损坏
    echo 4. 系统不兼容
    echo.
    set "INSTALL_SUCCESS=0"
)

echo.

echo [4] 安装后验证
echo ========================================

if "%INSTALL_SUCCESS%"=="1" (
    echo 正在验证安装结果...
    
    :: 等待注册表更新
    timeout /t 3 >nul
    
    :: 检查语音包注册
    reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" >nul 2>&1
    if %errorlevel% equ 0 (
        for /f %%i in ('reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Speech\Voices\Tokens" ^| find /c "HKEY"') do (
            set "NEW_VOICE_COUNT=%%i"
        )
        
        if !NEW_VOICE_COUNT! gtr %VOICE_COUNT% (
            echo [✓] 语音包注册成功，新增语音包数量: !NEW_VOICE_COUNT!
            set "REGISTRATION_OK=1"
        ) else (
            echo [!] 语音包可能未正确注册
            set "REGISTRATION_OK=0"
        )
    ) else (
        echo [!] 无法检查语音包注册状态
        set "REGISTRATION_OK=0"
    )
    
    :: 测试TTS功能
    echo.
    echo 正在测试TTS功能...
    
    :: 创建测试脚本
    echo Set objVoice = CreateObject("SAPI.SpVoice") > "%TEMP%\tts_test.vbs"
    echo On Error Resume Next >> "%TEMP%\tts_test.vbs"
    echo objVoice.Volume = 30 >> "%TEMP%\tts_test.vbs"
    echo objVoice.Speak "慧慧语音包安装成功，语音功能测试" >> "%TEMP%\tts_test.vbs"
    echo If Err.Number ^<^> 0 Then >> "%TEMP%\tts_test.vbs"
    echo     WScript.Quit 1 >> "%TEMP%\tts_test.vbs"
    echo End If >> "%TEMP%\tts_test.vbs"
    
    :: 运行测试
    cscript //nologo "%TEMP%\tts_test.vbs" >nul 2>&1
    set "TEST_RESULT=%errorlevel%"
    
    :: 清理测试文件
    del "%TEMP%\tts_test.vbs" >nul 2>&1
    
    if "%TEST_RESULT%"=="0" (
        echo [✓] TTS功能测试成功！
        echo 如果您听到了语音，说明慧慧语音包安装完成。
        set "TEST_OK=1"
    ) else (
        echo [!] TTS功能测试失败
        echo 可能需要重启计算机后才能正常工作。
        set "TEST_OK=0"
    )
) else (
    echo 由于安装失败，跳过验证步骤。
    set "REGISTRATION_OK=0"
    set "TEST_OK=0"
)

echo.

echo [5] 安装完成
echo ========================================

if "%INSTALL_SUCCESS%"=="1" (
    if "%REGISTRATION_OK%"=="1" and "%TEST_OK%"=="1" (
        echo [🎉] 慧慧语音包安装完全成功！
        echo.
        echo ✅ 安装状态：成功
        echo ✅ 注册状态：成功
        echo ✅ 功能测试：成功
        echo.
        echo 现在您可以：
        echo 1. 重新运行"我要记忆"程序
        echo 2. 享受高质量的慧慧中文语音
        echo 3. 在控制面板的语音设置中查看新语音
    ) else (
        echo [⚠️] 慧慧语音包安装部分成功
        echo.
        echo ✅ 安装状态：成功
        echo ⚠️ 注册状态：%REGISTRATION_OK%
        echo ⚠️ 功能测试：%TEST_OK%
        echo.
        echo 建议操作：
        echo 1. 重启计算机
        echo 2. 重新运行"我要记忆"程序
        echo 3. 如果仍有问题，运行诊断工具
    )
) else (
    echo [❌] 慧慧语音包安装失败
    echo.
    echo 可能的解决方案：
    echo 1. 确保以管理员身份运行此脚本
    echo 2. 检查文件是否完整（重新下载）
    echo 3. 先安装Speech Platform Runtime
    echo 4. 检查系统兼容性
    echo 5. 临时禁用防病毒软件
)

echo.
echo 重要提醒：
echo 1. 建议重启计算机以确保所有更改生效
echo 2. 重启后重新运行"我要记忆"程序
echo 3. 如果遇到问题，可以运行系统诊断工具
echo.

set /p restart_now="是否现在重启计算机? (Y/N): "
if /i "%restart_now%"=="Y" (
    echo 系统将在10秒后重启...
    shutdown /r /t 10 /c "慧慧语音包安装完成，重启系统使更改生效"
) else (
    echo 请记得稍后手动重启计算机以使语音功能完全生效。
)

echo.
echo 安装器执行完成。
pause
